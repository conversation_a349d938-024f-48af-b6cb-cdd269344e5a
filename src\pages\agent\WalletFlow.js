import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Form, Button, Pagination } from 'react-bootstrap';
import { FaDownload } from 'react-icons/fa';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';

const WalletFlow = () => {
    const { t } = useTranslation();
    const [transactions, setTransactions] = useState([]);
    const [filteredTransactions, setFilteredTransactions] = useState([]);
    const [loading, setLoading] = useState(true);

    // Filter states
    const [amountFilter, setAmountFilter] = useState(''); // 'positive', 'negative', or ''
    const [sourceFilter, setSourceFilter] = useState(''); // fil_source filter
    const [emailSearch, setEmailSearch] = useState(''); // customer email search
    const [startDate, setStartDate] = useState('');
    const [endDate, setEndDate] = useState('');

    // Pagination states
    const [currentPage, setCurrentPage] = useState(1);
    const [transactionsPerPage] = useState(10);
    const [paginatedTransactions, setPaginatedTransactions] = useState([]);

    useEffect(() => {
        const fetchWalletFlow = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            try {
                // First, get the agent profile to verify the user is an agent
                const { data: agentProfile, error: agentError } = await supabase
                    .from('agent_profiles')
                    .select('user_id, maker_id')
                    .eq('user_id', user.id)
                    .single();

                if (agentError) {
                    console.error('Error fetching agent profile:', agentError);
                    setLoading(false);
                    return;
                }

                // Fetch transactions directly using agent_id - much simpler and more efficient
                const { data, error } = await supabase
                    .from('transactions')
                    .select(`
                        id,
                        tx_date,
                        sender_user_id,
                        receiver_user_id,
                        amount_net,
                        tx_type,
                        filecoin_msg_id,
                        agent_id,
                        audit_id,
                        created_at,
                        sender:users!sender_user_id (
                            email
                        ),
                        receiver:users!receiver_user_id (
                            email
                        ),
                        audit_logs:audit_logs!transactions_audit_id_fkey (
                            diff
                        )
                    )
                    `)
                    .eq('agent_id', user.id)
                    .order('tx_date', { ascending: false })
                    .limit(100);

                if (error) {
                    console.error('Error fetching wallet flow:', error);
                    setTransactions([]);
                } else if (data && data.length > 0) {
                    // Process transactions to add balance information and determine user context
                    const processedTransactions = data.map(transaction => {
                        // Since we're querying by agent_id, all transactions are related to this agent's customers

                        let customerEmail = '';
                        let amount = transaction.amount_net;
                        let source = transaction.tx_type || '-';
                        let balanceBefore = 0;
                        let balanceAfter = 0;

                        // Determine customer email and amount direction
                        if (transaction.sender?.email && transaction.receiver?.email) {
                            // Both sender and receiver have emails, show both
                            customerEmail = `${transaction.sender.email} → ${transaction.receiver.email}`;
                        } else if (transaction.sender?.email) {
                            // Show sender email
                            customerEmail = transaction.sender.email;
                        } else if (transaction.receiver?.email) {
                            // Show receiver email
                            customerEmail = transaction.receiver.email;
                        } else {
                            customerEmail = '-';
                        }

                        // Extract balance information from audit_logs.diff for withdrawal transactions
                        if (transaction.tx_type === 'withdraw' && transaction.audit_logs && transaction.audit_logs.diff) {
                            try {
                                const diff = transaction.audit_logs.diff;
                                if (diff.old && diff.old.user_assets && diff.old.user_assets.balance_total !== undefined) {
                                    balanceBefore = parseFloat(diff.old.user_assets.balance_total) || 0;
                                }
                                if (diff.new && diff.new.user_assets && diff.new.user_assets.balance_total !== undefined) {
                                    balanceAfter = parseFloat(diff.new.user_assets.balance_total) || 0;
                                }
                            } catch (error) {
                                console.error('Error parsing audit_logs.diff for withdraw transaction:', transaction.id, error);
                            }
                        }

                        if (transaction.tx_type === 'manual_deposit' && transaction.audit_logs && transaction.audit_logs.diff) {
                            try {
                                const diff = transaction.audit_logs.diff;
                                if (diff.old && diff.old.user_assets && diff.old.user_assets.balance_total !== undefined) {
                                    balanceBefore = parseFloat(diff.old.user_assets.balance_total) || 0;
                                }
                                if (diff.new && diff.new.user_assets && diff.new.user_assets.balance_total !== undefined) {
                                    balanceAfter = parseFloat(diff.new.user_assets.balance_total) || 0;
                                }
                            } catch (error) {
                                console.error('Error parsing audit_logs.diff for manual_deposit transaction:', transaction.id, error);
                            }
                        }

                        if ((transaction.tx_type === 'order_distributions' || transaction.tx_type === 'vesting_release_daily') && transaction.audit_logs && transaction.audit_logs.diff) {
                            try {
                                const diff = transaction.audit_logs.diff;
                                if (diff.old && diff.old.user_assets && diff.old.user_assets.balance_total !== undefined) {
                                    balanceBefore = parseFloat(diff.old.user_assets.balance_total) || 0;
                                }
                                if (diff.new && diff.new.user_assets && diff.new.user_assets.balance_total !== undefined) {
                                    balanceAfter = parseFloat(diff.new.user_assets.balance_total) || 0;
                                }
                            } catch (error) {
                                console.error('Error parsing audit_logs.diff for order_distributions transaction:', transaction.id, error);
                            }
                        }

                        return {
                            ...transaction,
                            customer_email: customerEmail,
                            display_amount: amount,
                            fil_source: source,
                            balance_before: balanceBefore,
                            balance_after: balanceAfter
                        };
                    });

                    setTransactions(processedTransactions);
                } else {
                    setTransactions([]);
                }
            } catch (error) {
                console.error('Error in fetchWalletFlow:', error);
            }
            setLoading(false);
        };

        fetchWalletFlow();
    }, []);

    // Filter transactions based on filter criteria
    useEffect(() => {
        let filtered = transactions;

        // Filter by amount (positive/negative)
        if (amountFilter === 'positive') {
            filtered = filtered.filter(transaction => transaction.display_amount > 0);
        } else if (amountFilter === 'negative') {
            filtered = filtered.filter(transaction => transaction.display_amount < 0);
        }

        // Filter by fil_source
        if (sourceFilter) {
            filtered = filtered.filter(transaction => transaction.fil_source === sourceFilter);
        }

        // Filter by customer email search
        if (emailSearch) {
            filtered = filtered.filter(transaction =>
                transaction.customer_email.toLowerCase().includes(emailSearch.toLowerCase())
            );
        }

        // Filter by date range
        if (startDate) {
            filtered = filtered.filter(transaction =>
                new Date(transaction.tx_date) >= new Date(startDate)
            );
        }
        if (endDate) {
            filtered = filtered.filter(transaction =>
                new Date(transaction.tx_date) <= new Date(endDate)
            );
        }

        setFilteredTransactions(filtered);
        setCurrentPage(1); // Reset to first page when filters change
    }, [transactions, amountFilter, sourceFilter, emailSearch, startDate, endDate]);

    // Paginate filtered transactions
    useEffect(() => {
        const indexOfLastTransaction = currentPage * transactionsPerPage;
        const indexOfFirstTransaction = indexOfLastTransaction - transactionsPerPage;
        const currentTransactions = filteredTransactions.slice(indexOfFirstTransaction, indexOfLastTransaction);
        setPaginatedTransactions(currentTransactions);
    }, [filteredTransactions, currentPage, transactionsPerPage]);

    // Pagination handlers
    const handlePageChange = (pageNumber) => {
        setCurrentPage(pageNumber);
    };

    const totalPages = Math.ceil(filteredTransactions.length / transactionsPerPage);

    const formatAmount = (amount) => {
        if (amount === null || amount === undefined) return '0.000000';
        const num = parseFloat(amount);
        return num >= 0 ? `+${num.toFixed(6)}` : num.toFixed(6);
    };

    const getAmountColor = (amount) => {
        if (amount === null || amount === undefined) return 'text-muted';
        return parseFloat(amount) >= 0 ? 'text-success' : 'text-danger';
    };

    // Export filtered transactions to CSV
    const exportToCSV = () => {
        if (filteredTransactions.length === 0) {
            alert(t('no_data_to_export'));
            return;
        }

        // Define CSV headers
        const headers = [
            t('transaction_amount'),
            t('fil_source'),
            t('balance_before'),
            t('balance_after'),
            t('username'),
            t('tx_date')
        ];

        // Convert data to CSV format
        const csvData = filteredTransactions.map(transaction => [
            formatAmount(transaction.display_amount),
            t(transaction.fil_source),
            transaction.balance_before.toFixed(6),
            transaction.balance_after.toFixed(6),
            transaction.customer_email,
            new Date(transaction.tx_date).toLocaleString()
        ]);

        // Combine headers and data
        const csvContent = [headers, ...csvData]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');

        // Add UTF-8 BOM to ensure proper encoding for Japanese characters
        const BOM = '\uFEFF';
        const csvWithBOM = BOM + csvContent;

        // Create and download CSV file with proper UTF-8 encoding
        const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `wallet_flow_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const getTransactionTypeColor = (txType) => {
        switch (txType) {
            case 'deposit':
                return 'success';
            case 'manual_deposit':
                return 'success';
            case 'withdraw':
                return 'danger';
            case 'transfer':
                return 'primary';
            case 'order_distributions':
                return 'info';
            default:
                return 'secondary';
        }
    };

    if (loading) {
        return <div>{t('loading_wallet_flow')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('wallet_flow')}</h2>

            {/* Search and Filter Bar */}
            <Row className="mb-4">
                <Col>
                    <Card>
                        <Card.Body>
                            <Row className="align-items-end">
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('export_data')}</Form.Label>
                                        <div>
                                            <Button
                                                variant="success"
                                                onClick={exportToCSV}
                                                className="mb-2"
                                                disabled={filteredTransactions.length === 0}
                                            >
                                                <FaDownload className="me-1" />
                                                {t('export_all')}
                                            </Button>
                                        </div>
                                    </Form.Group>
                                </Col>
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('search_by_email')}</Form.Label>
                                        <Form.Control
                                            type="text"
                                            placeholder={t('search_by_email')}
                                            value={emailSearch}
                                            onChange={(e) => setEmailSearch(e.target.value)}
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('amount_filter')}</Form.Label>
                                        <Form.Select
                                            value={amountFilter}
                                            onChange={(e) => setAmountFilter(e.target.value)}
                                        >
                                            <option value="">{t('all_amounts')}</option>
                                            <option value="positive">{t('positive_amounts')}</option>
                                            <option value="negative">{t('negative_amounts')}</option>
                                        </Form.Select>
                                    </Form.Group>
                                </Col>
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('fil_source')}</Form.Label>
                                        <Form.Select
                                            value={sourceFilter}
                                            onChange={(e) => setSourceFilter(e.target.value)}
                                        >
                                            <option value="">{t('all_sources')}</option>
                                            <option value="withdraw">{t('withdraw')}</option>
                                            <option value="manual_deposit">{t('manual_deposit')}</option>
                                            <option value="transfer">{t('transfer')}</option>
                                        </Form.Select>
                                    </Form.Group>
                                </Col>
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('start_date')}</Form.Label>
                                        <Form.Control
                                            type="date"
                                            value={startDate}
                                            onChange={(e) => setStartDate(e.target.value)}
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('end_date')}</Form.Label>
                                        <Form.Control
                                            type="date"
                                            value={endDate}
                                            onChange={(e) => setEndDate(e.target.value)}
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('transaction_amount')}</th>
                                        <th>{t('fil_source')}</th>
                                        <th>{t('balance_before')}</th>
                                        <th>{t('balance_after')}</th>
                                        <th>{t('username')}</th>
                                        <th>{t('tx_date')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {paginatedTransactions.length === 0 ? (
                                        <tr>
                                            <td colSpan="7" className="text-center">{t('no_wallet_flow_available')}</td>
                                        </tr>
                                    ) : (
                                        paginatedTransactions.map((transaction) => (
                                            <tr key={transaction.id}>
                                                <td className={getAmountColor(transaction.display_amount)}>
                                                    <strong>{formatAmount(transaction.display_amount)}</strong>
                                                </td>
                                                <td>{t(transaction.fil_source)}</td>
                                                <td>{transaction.balance_before.toFixed(6)}</td>
                                                <td>{transaction.balance_after.toFixed(6)}</td>
                                                <td>{transaction.customer_email}</td>
                                                <td>{new Date(transaction.tx_date).toLocaleString()}</td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>

                            {/* Pagination */}
                            {totalPages > 1 && (
                                <div className="d-flex justify-content-center">
                                    <Pagination>
                                        <Pagination.First
                                            onClick={() => handlePageChange(1)}
                                            disabled={currentPage === 1}
                                        />
                                        <Pagination.Prev
                                            onClick={() => handlePageChange(currentPage - 1)}
                                            disabled={currentPage === 1}
                                        />

                                        {/* Show page numbers */}
                                        {[...Array(totalPages)].map((_, index) => {
                                            const pageNumber = index + 1;
                                            // Show first page, last page, current page, and pages around current page
                                            if (
                                                pageNumber === 1 ||
                                                pageNumber === totalPages ||
                                                (pageNumber >= currentPage - 2 && pageNumber <= currentPage + 2)
                                            ) {
                                                return (
                                                    <Pagination.Item
                                                        key={pageNumber}
                                                        active={pageNumber === currentPage}
                                                        onClick={() => handlePageChange(pageNumber)}
                                                    >
                                                        {pageNumber}
                                                    </Pagination.Item>
                                                );
                                            } else if (
                                                pageNumber === currentPage - 3 ||
                                                pageNumber === currentPage + 3
                                            ) {
                                                return <Pagination.Ellipsis key={pageNumber} />;
                                            }
                                            return null;
                                        })}

                                        <Pagination.Next
                                            onClick={() => handlePageChange(currentPage + 1)}
                                            disabled={currentPage === totalPages}
                                        />
                                        <Pagination.Last
                                            onClick={() => handlePageChange(totalPages)}
                                            disabled={currentPage === totalPages}
                                        />
                                    </Pagination>
                                </div>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default WalletFlow;