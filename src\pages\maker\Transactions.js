import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Badge, Form, Button, InputGroup, Pagination } from 'react-bootstrap';
import { FaDownload } from 'react-icons/fa';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';

const Transactions = () => {
    const { t } = useTranslation();
    const [transactions, setTransactions] = useState([]);
    const [filteredTransactions, setFilteredTransactions] = useState([]);
    const [loading, setLoading] = useState(true);

    // Pagination states
    const [currentPage, setCurrentPage] = useState(1);
    const [transactionsPerPage] = useState(10);
    const [paginatedTransactions, setPaginatedTransactions] = useState([]);

    // Filter states
    const [emailSearch, setEmailSearch] = useState(''); // sender/receiver email search
    const [txTypeFilter, setTxTypeFilter] = useState(''); // transaction type filter
    const [amountFilter, setAmountFilter] = useState(''); // 'positive', 'negative', or ''
    const [startDate, setStartDate] = useState('');
    const [endDate, setEndDate] = useState('');

    useEffect(() => {
        const fetchTransactions = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            // Fetch transactions with user information
            const { data, error } = await supabase
                .from('transactions')
                .select(`
                    id,
                    tx_date,
                    sender_user_id,
                    receiver_user_id,
                    amount_net,
                    tx_type,
                    filecoin_msg_id,
                    agent_id,
                    created_at,
                    sender:users!sender_user_id (
                        email
                    ),
                    receiver:users!receiver_user_id (
                        email
                    ),
                    agent:agent_profiles!agent_id (
                        user_id,
                        users (
                            email
                        )
                    )
                `)
                .order('tx_date', { ascending: false });

            if (error) {
                console.error('Error fetching transactions:', error);
            } else {
                setTransactions(data);
            }
            setLoading(false);
        };

        fetchTransactions();
    }, []);

    // Filter transactions based on filter criteria
    useEffect(() => {
        let filtered = transactions;

        // Filter by email search (sender or receiver)
        if (emailSearch) {
            filtered = filtered.filter(transaction =>
                (transaction.sender?.email && transaction.sender.email.toLowerCase().includes(emailSearch.toLowerCase())) ||
                (transaction.receiver?.email && transaction.receiver.email.toLowerCase().includes(emailSearch.toLowerCase()))
            );
        }

        // Filter by transaction type
        if (txTypeFilter) {
            filtered = filtered.filter(transaction => transaction.tx_type === txTypeFilter);
        }

        // Filter by amount (positive/negative)
        if (amountFilter === 'positive') {
            filtered = filtered.filter(transaction => parseFloat(transaction.amount_net) > 0);
        } else if (amountFilter === 'negative') {
            filtered = filtered.filter(transaction => parseFloat(transaction.amount_net) < 0);
        }

        // Filter by date range
        if (startDate) {
            filtered = filtered.filter(transaction =>
                new Date(transaction.tx_date) >= new Date(startDate)
            );
        }
        if (endDate) {
            filtered = filtered.filter(transaction =>
                new Date(transaction.tx_date) <= new Date(endDate)
            );
        }

        setFilteredTransactions(filtered);
        setCurrentPage(1); // Reset to first page when filters change
    }, [transactions, emailSearch, txTypeFilter, amountFilter, startDate, endDate]);

    // Paginate filtered transactions
    useEffect(() => {
        const indexOfLastTransaction = currentPage * transactionsPerPage;
        const indexOfFirstTransaction = indexOfLastTransaction - transactionsPerPage;
        const currentTransactions = filteredTransactions.slice(indexOfFirstTransaction, indexOfLastTransaction);
        setPaginatedTransactions(currentTransactions);
    }, [filteredTransactions, currentPage, transactionsPerPage]);

    // Pagination handlers
    const handlePageChange = (pageNumber) => {
        setCurrentPage(pageNumber);
    };

    const totalPages = Math.ceil(filteredTransactions.length / transactionsPerPage);

    // Export filtered transactions to CSV
    const exportToCSV = () => {
        if (filteredTransactions.length === 0) {
            alert(t('no_data_to_export'));
            return;
        }

        // Define CSV headers
        const headers = [
            t('transaction_id'),
            t('tx_date'),
            t('sender'),
            t('receiver'),
            t('amount'),
            t('tx_type'),
            t('filecoin_msg_id'),
            t('agent'),
            t('created_at')
        ];

        // Convert data to CSV format
        const csvData = filteredTransactions.map(transaction => [
            transaction.id,
            new Date(transaction.tx_date).toLocaleString(),
            transaction.sender?.email || '-',
            transaction.receiver?.email || '-',
            transaction.amount_net ? Number(transaction.amount_net).toFixed(6) + ' FIL' : '0 FIL',
            t(transaction.tx_type) || 'unknown',
            transaction.filecoin_msg_id || '-',
            transaction.agent?.users?.email || '-',
            new Date(transaction.created_at).toLocaleString()
        ]);

        // Combine headers and data
        const csvContent = [headers, ...csvData]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');

        // Add UTF-8 BOM to ensure proper encoding for Japanese characters
        const BOM = '\uFEFF';
        const csvWithBOM = BOM + csvContent;

        // Create and download CSV file with proper UTF-8 encoding
        const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `transactions_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const getTransactionTypeColor = (txType) => {
        switch (txType) {
            case 'deposit':
                return 'success';
            case 'manual_deposit':
                return 'success';
            case 'withdraw':
                return 'danger';
            case 'transfer':
                return 'primary';
            case 'order_distributions':
                return 'info';
            default:
                return 'secondary';
        }
    };

    if (loading) {
        return <div>{t('loading_transactions')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('transactions')}</h2>

            {/* Search and Filter Bar */}
            <Row className="mb-4">
                <Col>
                    <Card>
                        <Card.Body>
                            <Row className="align-items-end">
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('export_data')}</Form.Label>
                                        <div>
                                            <Button
                                                variant="success"
                                                onClick={exportToCSV}
                                                className="mb-2"
                                                disabled={filteredTransactions.length === 0}
                                            >
                                                <FaDownload className="me-1" />
                                                {t('export_all')}
                                            </Button>
                                        </div>
                                    </Form.Group>
                                </Col>
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('search_by_email')}</Form.Label>
                                        <Form.Control
                                            type="text"
                                            placeholder={t('search_by_email')}
                                            value={emailSearch}
                                            onChange={(e) => setEmailSearch(e.target.value)}
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('transaction_type')}</Form.Label>
                                        <Form.Select
                                            value={txTypeFilter}
                                            onChange={(e) => setTxTypeFilter(e.target.value)}
                                        >
                                            <option value="">{t('all_types')}</option>
                                            <option value="deposit">{t('deposit')}</option>
                                            <option value="manual_deposit">{t('manual_deposit')}</option>
                                            <option value="withdraw">{t('withdraw')}</option>
                                            <option value="transfer">{t('transfer')}</option>
                                            <option value="order_distributions">{t('order_distributions')}</option>
                                            <option value="vesting_release_daily">{t('vesting_release_daily')}</option>
                                        </Form.Select>
                                    </Form.Group>
                                </Col>
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('amount_filter')}</Form.Label>
                                        <Form.Select
                                            value={amountFilter}
                                            onChange={(e) => setAmountFilter(e.target.value)}
                                        >
                                            <option value="">{t('all_amounts')}</option>
                                            <option value="positive">{t('positive_amounts')}</option>
                                            <option value="negative">{t('negative_amounts')}</option>
                                        </Form.Select>
                                    </Form.Group>
                                </Col>
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('start_date')}</Form.Label>
                                        <Form.Control
                                            type="date"
                                            value={startDate}
                                            onChange={(e) => setStartDate(e.target.value)}
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('end_date')}</Form.Label>
                                        <Form.Control
                                            type="date"
                                            value={endDate}
                                            onChange={(e) => setEndDate(e.target.value)}
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('transaction_id')}</th>
                                        <th>{t('tx_date')}</th>
                                        <th>{t('sender')}</th>
                                        <th>{t('receiver')}</th>
                                        <th>{t('amount')}</th>
                                        <th>{t('tx_type')}</th>
                                        <th>{t('filecoin_msg_id')}</th>
                                        <th>{t('agent')}</th>
                                        <th>{t('created_at')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {paginatedTransactions.length === 0 ? (
                                        <tr>
                                            <td colSpan="9" className="text-center">{t('no_transactions_available')}</td>
                                        </tr>
                                    ) : (
                                        paginatedTransactions.map((transaction) => (
                                            <tr key={transaction.id}>
                                                <td>{transaction.id.substring(0, 8)}...</td>
                                                <td>{new Date(transaction.tx_date).toLocaleString()}</td>
                                                <td>{transaction.sender?.email || '-'}</td>
                                                <td>{transaction.receiver?.email || '-'}</td>
                                                <td>{transaction.amount_net ? Number(transaction.amount_net).toFixed(6) : '0'} FIL</td>
                                                <td>
                                                    <Badge bg={getTransactionTypeColor(transaction.tx_type)}>
                                                        {t(transaction.tx_type) || '-'}
                                                    </Badge>
                                                </td>
                                                <td>
                                                    {transaction.filecoin_msg_id ? (
                                                        <span title={transaction.filecoin_msg_id}>
                                                            {transaction.filecoin_msg_id.substring(0, 10)}...
                                                        </span>
                                                    ) : '-'}
                                                </td>
                                                <td>{transaction.agent?.users?.email || '-'}</td>
                                                <td>{new Date(transaction.created_at).toLocaleString()}</td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>

                            {/* Pagination */}
                            {totalPages > 1 && (
                                <div className="d-flex justify-content-center">
                                    <Pagination>
                                        <Pagination.First
                                            onClick={() => handlePageChange(1)}
                                            disabled={currentPage === 1}
                                        />
                                        <Pagination.Prev
                                            onClick={() => handlePageChange(currentPage - 1)}
                                            disabled={currentPage === 1}
                                        />

                                        {/* Show page numbers */}
                                        {[...Array(totalPages)].map((_, index) => {
                                            const pageNumber = index + 1;
                                            // Show first page, last page, current page, and pages around current page
                                            if (
                                                pageNumber === 1 ||
                                                pageNumber === totalPages ||
                                                (pageNumber >= currentPage - 2 && pageNumber <= currentPage + 2)
                                            ) {
                                                return (
                                                    <Pagination.Item
                                                        key={pageNumber}
                                                        active={pageNumber === currentPage}
                                                        onClick={() => handlePageChange(pageNumber)}
                                                    >
                                                        {pageNumber}
                                                    </Pagination.Item>
                                                );
                                            } else if (
                                                pageNumber === currentPage - 3 ||
                                                pageNumber === currentPage + 3
                                            ) {
                                                return <Pagination.Ellipsis key={pageNumber} />;
                                            }
                                            return null;
                                        })}

                                        <Pagination.Next
                                            onClick={() => handlePageChange(currentPage + 1)}
                                            disabled={currentPage === totalPages}
                                        />
                                        <Pagination.Last
                                            onClick={() => handlePageChange(totalPages)}
                                            disabled={currentPage === totalPages}
                                        />
                                    </Pagination>
                                </div>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default Transactions;
