# Withdraw Logic Test Plan

## 测试环境准备

1. 确保有一个测试用户账户，余额为100 FIL
2. 确保有agent或maker账户用于审批
3. 确保数据库连接正常

## 测试用例

### 测试用例1：正常提交和审批通过流程

**前置条件：**
- 用户余额：100 FIL (balance_available: 100, balance_total: 100, withdrawn_total: 0)

**测试步骤：**
1. 用户提交10 FIL的withdraw申请
2. 检查用户余额变化
3. Agent/Maker审批通过
4. 检查最终余额状态

**预期结果：**
- 提交后：balance_available: 90, balance_total: 90, withdrawn_total: 0
- 审批通过后：balance_available: 90, balance_total: 90, withdrawn_total: 9.99

### 测试用例2：提交后审批拒绝流程

**前置条件：**
- 用户余额：100 FIL (balance_available: 100, balance_total: 100, withdrawn_total: 0)

**测试步骤：**
1. 用户提交10 FIL的withdraw申请
2. 检查用户余额变化
3. Agent/Maker审批拒绝
4. 检查最终余额状态

**预期结果：**
- 提交后：balance_available: 90, balance_total: 90, withdrawn_total: 0
- 审批拒绝后：balance_available: 100, balance_total: 100, withdrawn_total: 0

### 测试用例3：余额不足的情况

**前置条件：**
- 用户余额：5 FIL

**测试步骤：**
1. 用户尝试提交10 FIL的withdraw申请

**预期结果：**
- 提交失败，显示余额不足错误
- 用户余额不变

### 测试用例4：多次提交申请

**前置条件：**
- 用户余额：100 FIL

**测试步骤：**
1. 用户提交30 FIL的withdraw申请
2. 用户再次尝试提交50 FIL的withdraw申请

**预期结果：**
- 第一次提交成功：balance_available: 70
- 第二次提交成功：balance_available: 20
- 两个withdrawal记录都存在且状态为pending

### 测试用例5：系统错误回滚测试

**测试步骤：**
1. 模拟在扣款成功后，创建withdrawal记录失败的情况
2. 检查用户余额是否被正确回滚

**预期结果：**
- 用户余额应该回滚到原始状态
- 不应该有withdrawal记录被创建

## 验证点

### 数据库验证
1. **user_assets表**：检查balance_available, balance_total, withdrawn_total字段
2. **withdrawals表**：检查withdrawal记录的创建和状态更新
3. **audit_logs表**：检查审批操作的日志记录
4. **transactions表**：检查approved withdrawal的交易记录

### 前端验证
1. **客户页面**：余额显示是否实时更新
2. **审批页面**：操作是否成功，错误处理是否正确
3. **用户体验**：提交后立即看到余额变化

## 测试数据

```sql
-- 查看用户资产
SELECT * FROM user_assets WHERE user_id = 'test-user-id';

-- 查看withdrawal记录
SELECT * FROM withdrawals WHERE user_id = 'test-user-id' ORDER BY requested_at DESC;

-- 查看审批日志
SELECT * FROM audit_logs WHERE object_table = 'withdrawals' ORDER BY created_at DESC;

-- 查看交易记录
SELECT * FROM transactions WHERE tx_type = 'withdraw' ORDER BY created_at DESC;
```

## 回归测试

确保修改后以下功能仍然正常：
1. 用户登录和认证
2. 其他类型的交易（如果有）
3. 资产显示和计算
4. 邮件通知功能
5. 密码验证功能

## 性能测试

1. 并发提交withdraw申请
2. 大量数据下的查询性能
3. 数据库事务的性能影响
