body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubunt<PERSON>', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #ffffff;
  min-height: 100vh;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* FIL Platform App Styles */
.fil-platform-app {
  min-height: 100vh;
  background: #ffffff;
}

/* Modern Navbar Styles - Filfox.info inspired */
.modern-navbar {
  background: #ffffff !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid #e9ecef;
  padding: 0.75rem 0;
}

.modern-navbar .navbar-brand {
  font-weight: 600;
  font-size: 1.4rem;
  color: #2c3e50 !important;
  text-decoration: none;
}

.brand-text {
  color: #2c3e50;
  font-weight: 600;
}

/* Remove underline from all links */
a {
  text-decoration: none !important;
}

a:hover {
  text-decoration: none !important;
}

.nav-link-modern {
  color: #6c757d !important;
  font-weight: 500;
  padding: 0.5rem 1rem !important;
  border-radius: 6px;
  margin: 0 0.25rem;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  text-decoration: none !important;
}

.nav-link-modern:hover {
  color: #007bff !important;
  background: #f8f9fa;
  text-decoration: none !important;
}

.nav-dropdown-modern .dropdown-toggle {
  color: #6c757d !important;
  font-weight: 500;
  padding: 0.5rem 1rem !important;
  border-radius: 6px;
  margin: 0 0.25rem;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  text-decoration: none !important;
}

.nav-dropdown-modern .dropdown-toggle:hover {
  color: #007bff !important;
  background: #f8f9fa;
  text-decoration: none !important;
}

.nav-dropdown-modern .dropdown-menu {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 0.5rem;
  margin-top: 0.25rem;
}

.dropdown-item-modern {
  color: #495057 !important;
  padding: 0.5rem 1rem !important;
  border-radius: 4px;
  font-weight: 400;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  text-decoration: none !important;
}

.dropdown-item-modern:hover {
  background: #007bff !important;
  color: white !important;
  text-decoration: none !important;
}

/* Login Page Styles */
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 2rem 0;
  margin-top: -20vh; /* より上に移動 */
}

.login-card {
  width: 400px;
  max-width: 90vw;
  margin: 0 auto;
}

/* User dropdown menu - prevent cutoff */
.nav-dropdown-modern .dropdown-menu {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 0.5rem;
  margin-top: 0.25rem;
  z-index: 9999; /* 高いz-indexで他の要素の上に表示 */
  max-height: 300px; /* 最大高さを制限 */
  overflow-y: auto; /* 必要に応じてスクロール */
  margin-right: 0; /* 右マージンを0に */
  margin-left: auto; /* 左マージンを自動調整 */
  min-width: 200px; /* 最小幅を設定 */
}

/* Ensure dropdown is positioned correctly */
.nav-dropdown-modern {
  position: relative;
}

/* ユーザーメニューが画面右端で切れないように調整 */
.nav-dropdown-modern .dropdown-menu.show {
  right: 0;
  left: auto;
}

/* Main Container Styles */
.main-container {
  padding: 2rem 0;
  min-height: calc(100vh - 80px);
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  flex-direction: column;
}

.loading-spinner {
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.connection-error {
  text-align: center;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 2rem 0;
}

.connection-error h4 {
  color: #dc3545;
  margin-bottom: 1rem;
}

.content-wrapper {
  background: white;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-bottom: 2rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .main-container {
    padding: 1rem 0;
  }

  .content-wrapper {
    padding: 1rem;
    margin-bottom: 1rem;
  }

  .modern-navbar .navbar-brand {
    font-size: 1.2rem;
  }

  .nav-link-modern,
  .nav-dropdown-modern .dropdown-toggle {
    padding: 0.4rem 0.8rem !important;
    font-size: 0.85rem;
  }
}

/* Card Styles */
.card {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.2s ease;
}

.card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  font-weight: 600;
  color: #495057;
  padding: 1rem 1.25rem;
}

/* Improved Card Colors for Referral Stats - Simplified */
.card.bg-primary {
  background: #ffffff !important;
  border-color: #e9ecef;
  color: #495057;
  border-left: 4px solid #007bff;
}

.card.bg-success {
  background: #ffffff !important;
  border-color: #e9ecef;
  color: #495057;
  border-left: 4px solid #28a745;
}

.card.bg-info {
  background: #ffffff !important;
  border-color: #e9ecef;
  color: #495057;
  border-left: 4px solid #17a2b8;
}

.card.bg-warning {
  background: #ffffff !important;
  border-color: #e9ecef;
  color: #495057;
  border-left: 4px solid #ffc107;
}

.card.bg-danger {
  background: #ffffff !important;
  border-color: #e9ecef;
  color: #495057;
  border-left: 4px solid #dc3545;
}

/* Additional card styles for better consistency */
.card.bg-secondary {
  background: #ffffff !important;
  border-color: #e9ecef;
  color: #495057;
  border-left: 4px solid #6c757d;
}

.card.bg-dark {
  background: #ffffff !important;
  border-color: #e9ecef;
  color: #495057;
  border-left: 4px solid #343a40;
}

.card.bg-light {
  background: #ffffff !important;
  border-color: #e9ecef;
  color: #495057;
  border-left: 4px solid #f8f9fa;
}

/* Button Styles */
.btn-primary {
  background: #007bff;
  border: 1px solid #007bff;
  border-radius: 6px;
  font-weight: 500;
  padding: 0.5rem 1rem;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background: #0056b3;
  border-color: #0056b3;
  transform: translateY(-1px);
}

.btn-secondary {
  background: #6c757d;
  border: 1px solid #6c757d;
  border-radius: 6px;
  font-weight: 500;
  padding: 0.5rem 1rem;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background: #545b62;
  border-color: #545b62;
  transform: translateY(-1px);
}

/* Edit button (info variant) - White text */
.btn-info {
  background: #17a2b8;
  border: 1px solid #17a2b8;
  border-radius: 6px;
  font-weight: 500;
  padding: 0.5rem 1rem;
  transition: all 0.2s ease;
  color: white !important;
}

.btn-info:hover {
  background: #138496;
  border-color: #138496;
  color: white !important;
  transform: translateY(-1px);
}

.btn-info:focus {
  background: #138496;
  border-color: #138496;
  color: white !important;
  box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25);
}

/* Danger button - White text */
.btn-danger {
  background: #dc3545;
  border: 1px solid #dc3545;
  border-radius: 6px;
  font-weight: 500;
  padding: 0.5rem 1rem;
  transition: all 0.2s ease;
  color: white !important;
}

.btn-danger:hover {
  background: #c82333;
  border-color: #c82333;
  color: white !important;
  transform: translateY(-1px);
}

.btn-danger:focus {
  background: #c82333;
  border-color: #c82333;
  color: white !important;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* Table Styles */
.table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.table thead th {
  background: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  color: #495057;
  font-weight: 600;
  padding: 1rem;
}

.table tbody tr {
  border-bottom: 1px solid #e9ecef;
}

.table tbody tr:hover {
  background: #f8f9fa;
}

/* Form Styles */
.form-control {
  border: 1px solid #ced4da;
  border-radius: 6px;
  padding: 0.5rem 0.75rem;
  transition: border-color 0.2s ease;
}

.form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Alert Styles */
.alert {
  border-radius: 8px;
  border: none;
  padding: 1rem 1.25rem;
  margin-bottom: 1rem;
}

.alert-success {
  background: #d4edda;
  color: #155724;
}

.alert-danger {
  background: #f8d7da;
  color: #721c24;
}

.alert-warning {
  background: #fff3cd;
  color: #856404;
}

.alert-info {
  background: #d1ecf1;
  color: #0c5460;
}

/* StatCard Styles - Proper text colors for different variants */
.card.bg-primary {
  background: #007bff !important;
  border-color: #007bff;
  color: white;
}

.card.bg-primary .card-title,
.card.bg-primary h3,
.card.bg-primary p,
.card.bg-primary span {
  color: white !important;
}

.card.bg-success {
  background: #28a745 !important;
  border-color: #28a745;
  color: white;
}

.card.bg-success .card-title,
.card.bg-success h3,
.card.bg-success p,
.card.bg-success span {
  color: white !important;
}

.card.bg-info {
  background: #17a2b8 !important;
  border-color: #17a2b8;
  color: white;
}

.card.bg-info .card-title,
.card.bg-info h3,
.card.bg-info p,
.card.bg-info span {
  color: white !important;
}

.card.bg-warning {
  background: #ffc107 !important;
  border-color: #ffc107;
  color: #212529;
}

.card.bg-warning .card-title,
.card.bg-warning h3,
.card.bg-warning p,
.card.bg-warning span {
  color: #212529 !important;
}
