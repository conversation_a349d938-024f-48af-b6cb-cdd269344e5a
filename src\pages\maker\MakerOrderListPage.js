import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Button, Modal, Form, Alert, InputGroup, Pagination } from 'react-bootstrap';
import { getSupabase, getCurrentMakerId } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';
import { FaTimes, FaPlus, FaDownload, FaImage } from "react-icons/fa";
import StatusBadge from '../../components/StatusBadge';

const MakerOrderListPage = () => {
    const { t } = useTranslation();
    const [orders, setOrders] = useState([]);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [startDate, setStartDate] = useState('');
    const [endDate, setEndDate] = useState('');
    const [reviewStatus, setReviewStatus] = useState('');
    const [filteredOrders, setFilteredOrders] = useState([]);

    // Pagination states
    const [currentPage, setCurrentPage] = useState(1);
    const [ordersPerPage] = useState(10);
    const [paginatedOrders, setPaginatedOrders] = useState([]);

    // Add Order Modal states
    const [showAddOrderModal, setShowAddOrderModal] = useState(false);
    const [addOrderLoading, setAddOrderLoading] = useState(false);
    const [addOrderError, setAddOrderError] = useState('');
    const [addOrderSuccess, setAddOrderSuccess] = useState('');
    const [products, setProducts] = useState([]);
    const [customers, setCustomers] = useState([]);
    const [agents, setAgents] = useState([]);

    // Edit Order Modal states
    const [showEditOrderModal, setShowEditOrderModal] = useState(false);
    const [editOrderLoading, setEditOrderLoading] = useState(false);
    const [editOrderError, setEditOrderError] = useState('');
    const [editOrderSuccess, setEditOrderSuccess] = useState('');
    const [editingOrder, setEditingOrder] = useState(null);

    // Delete Order Modal states
    const [showDeleteOrderModal, setShowDeleteOrderModal] = useState(false);
    const [deleteOrderLoading, setDeleteOrderLoading] = useState(false);
    const [deleteOrderError, setDeleteOrderError] = useState('');
    const [deleteOrderSuccess, setDeleteOrderSuccess] = useState('');
    const [deletingOrder, setDeletingOrder] = useState(null);
    const [deletePassword, setDeletePassword] = useState('');

    // Image Modal states
    const [showImageModal, setShowImageModal] = useState(false);
    const [selectedImage, setSelectedImage] = useState(null);

    // Form data for editing
    const [orderForm, setOrderForm] = useState({
        shares: '',
        storage_cost: '',
        total_rate: '',
        start_at: '',
        end_at: '',
        review_status: 'pending'
    });

    // Form data for adding new order
    const [newOrderForm, setNewOrderForm] = useState({
        product_id: '',
        customer_id: '',
        agent_id: '',
        shares: '',
        storage_cost: '',
        start_at: '',
        end_at: '',
        review_status: 'pending'
    });

    useEffect(() => {
        const fetchData = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);

            // Get maker ID (works for both maker and technician roles)
            const makerId = await getCurrentMakerId();
            if (!makerId) {
                setLoading(false);
                return; // User not authenticated or not authorized
            }

            // Fetch orders associated with products from this maker
            const { data: ordersData, error: ordersError } = await supabase
                .from('orders')
                .select(`
                    id,
                    product_id,
                    shares,
                    proof_image_url,
                    storage_cost,
                    total_rate,
                    start_at,
                    end_at,
                    review_status,
                    created_at,
                    products ( name, maker_id ),
                    customer_profiles (
                        real_name,
                        users (
                            email
                        )
                    ),
                    agent_profiles ( brand_name )
                `)
                .filter('products.maker_id', 'eq', makerId) // Filter by maker_id from products table
                .is('deleted_at', null)      
                .order('created_at', { ascending: false });

            if (ordersError) {
                console.error('Error fetching orders:', ordersError);
            } else {
                setOrders(ordersData);
            }

            // Fetch products for dropdown
            const { data: productsData, error: productsError } = await supabase
                .from('products')
                .select(`
                    id,
                    name,
                    category,
                    price,
                    total_shares,
                    sold_shares,
                    min_purchase,
                    tech_commission_pct,
                    ops_commission_pct,
                    commission_agent_pct
                `)
                .eq('maker_id', makerId)
                .eq('is_disabled', false)
                .order('created_at', { ascending: false });

            if (productsError) {
                console.error('Error fetching products:', productsError);
            } else {
                setProducts(productsData || []);
            }

            // Fetch customers for dropdown
            const { data: customersData, error: customersError } = await supabase
                .from('customer_profiles')
                .select(`
                    user_id,
                    real_name,
                    users (
                        email
                    )
                `)
                .order('created_at', { ascending: false });

            if (customersError) {
                console.error('Error fetching customers:', customersError);
            } else {
                setCustomers(customersData || []);
            }

            // Fetch agents for dropdown
            const { data: agentsData, error: agentsError } = await supabase
                .from('agent_profiles')
                .select(`
                    user_id,
                    brand_name,
                    users (
                        email
                    )
                `)
                .eq('maker_id', makerId)
                .order('created_at', { ascending: false });

            if (agentsError) {
                console.error('Error fetching agents:', agentsError);
            } else {
                setAgents(agentsData || []);
            }

            setLoading(false);
        };

        fetchData();
    }, []);

    // Helper function to convert hex string to string
    const hexToString = (hex) => {
        let result = '';
        for (let i = 0; i < hex.length; i += 2) {
            result += String.fromCharCode(parseInt(hex.substr(i, 2), 16));
        }
        return result;
    };

    // Helper function to get image source for display
    const getImageSrc = (imageData) => {
        if (!imageData) return null;

        // If it's already a data URL, return as is
        if (typeof imageData === 'string' && imageData.startsWith('data:')) {
            return imageData;
        }

        // Handle PostgreSQL bytea hex format (starts with \x)
        if (typeof imageData === 'string' && imageData.startsWith('\\x')) {
            try {
                // Remove the \x prefix and convert hex to string
                const hexData = imageData.substring(2);
                const decodedString = hexToString(hexData);

                // The decoded string should be a data URL
                if (decodedString.startsWith('data:')) {
                    return decodedString;
                }

                // If it's just base64 data, add the data URL prefix
                return `data:image/jpeg;base64,${decodedString}`;
            } catch (error) {
                console.error('Error converting hex data:', error);
                return null;
            }
        }

        // Handle Supabase bytea field - it might be returned as a Uint8Array or Buffer
        if (imageData instanceof Uint8Array || (imageData && imageData.type === 'Buffer')) {
            try {
                // Convert Uint8Array or Buffer to base64
                let base64String;
                if (imageData instanceof Uint8Array) {
                    // Convert Uint8Array to base64
                    base64String = btoa(String.fromCharCode.apply(null, imageData));
                } else if (imageData.type === 'Buffer' && imageData.data) {
                    // Handle Node.js Buffer format from Supabase
                    const uint8Array = new Uint8Array(imageData.data);
                    base64String = btoa(String.fromCharCode.apply(null, uint8Array));
                } else {
                    return null;
                }
                return `data:image/jpeg;base64,${base64String}`;
            } catch (error) {
                console.error('Error converting image data:', error);
                return null;
            }
        }

        return null;
    };

    // Handle image view
    const handleViewImage = (imageData) => {
        const imageSrc = getImageSrc(imageData);
        if (imageSrc) {
            setSelectedImage(imageSrc);
            setShowImageModal(true);
        }
    };

    // Filter orders based on search criteria
    useEffect(() => {
        let filtered = orders;

        // Search by customer email or real_name
        if (searchTerm) {
            filtered = filtered.filter(order =>
                order.customer_profiles?.users?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                order.customer_profiles?.real_name?.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        // Filter by date range
        if (startDate) {
            filtered = filtered.filter(order =>
                new Date(order.created_at) >= new Date(startDate)
            );
        }
        if (endDate) {
            filtered = filtered.filter(order =>
                new Date(order.created_at) <= new Date(endDate)
            );
        }

        // Filter by review status
        if (reviewStatus) {
            filtered = filtered.filter(order =>
                order.review_status === reviewStatus
            );
        }

        setFilteredOrders(filtered);
        setCurrentPage(1); // Reset to first page when filters change
    }, [orders, searchTerm, startDate, endDate, reviewStatus]);

    // Paginate filtered orders
    useEffect(() => {
        const indexOfLastOrder = currentPage * ordersPerPage;
        const indexOfFirstOrder = indexOfLastOrder - ordersPerPage;
        const currentOrders = filteredOrders.slice(indexOfFirstOrder, indexOfLastOrder);
        setPaginatedOrders(currentOrders);
    }, [filteredOrders, currentPage, ordersPerPage]);

    // Pagination handlers
    const handlePageChange = (pageNumber) => {
        setCurrentPage(pageNumber);
    };

    const totalPages = Math.ceil(filteredOrders.length / ordersPerPage);

    // Export filtered orders to CSV
    const exportToCSV = () => {
        if (filteredOrders.length === 0) {
            alert(t('no_data_to_export'));
            return;
        }

        // Define CSV headers
        const headers = [
            t('order_id'),
            t('product_name'),
            t('customer'),
            t('agent'),
            t('shares'),
            t('storage_cost'),
            t('total_rate'),
            t('start_at'),
            t('end_at'),
            t('status'),
            t('created_at')
        ];

        // Convert data to CSV format
        const csvData = filteredOrders.map(order => [
            order.id,
            order.products?.name || 'N/A',
            order.customer_profiles?.real_name || order.customer_profiles?.users?.email || 'N/A',
            order.agent_profiles?.brand_name || 'N/A',
            order.shares || '0',
            order.storage_cost || '0',
            order.total_rate || '0',
            order.start_at || '-',
            order.end_at || '-',
            t(order.review_status) || 'pending',
            new Date(order.created_at).toLocaleString()
        ]);

        // Combine headers and data
        const csvContent = [headers, ...csvData]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');

        // Add UTF-8 BOM to ensure proper encoding for Japanese characters
        const BOM = '\uFEFF';
        const csvWithBOM = BOM + csvContent;

        // Create and download CSV file with proper UTF-8 encoding
        const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `maker_orders_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    // Form handling functions
    const handleFormChange = (field, value) => {
        setOrderForm(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleNewOrderFormChange = (field, value) => {
        setNewOrderForm(prev => {
            const updated = {
                ...prev,
                [field]: value
            };

            // Auto-calculate storage_cost when shares or product_id changes
            if (field === 'shares' || field === 'product_id') {
                const selectedProduct = products.find(product => product.id === (field === 'product_id' ? value : updated.product_id));
                if (selectedProduct && updated.shares) {
                    const shares = parseFloat(updated.shares);
                    const price = parseFloat(selectedProduct.price);
                    if (!isNaN(shares) && !isNaN(price)) {
                        updated.storage_cost = (shares * price).toFixed(6);
                    }
                }
            }

            return updated;
        });
    };

    // Get selected product details for auto-calculating total_rate
    const getSelectedProduct = () => {
        return products.find(product => product.id === newOrderForm.product_id);
    };

    // Calculate storage_cost from selected product's price and shares
    const calculateStorageCost = () => {
        const selectedProduct = getSelectedProduct();
        if (selectedProduct && newOrderForm.shares) {
            const shares = parseFloat(newOrderForm.shares);
            const price = parseFloat(selectedProduct.price);
            if (!isNaN(shares) && !isNaN(price)) {
                return (shares * price).toFixed(6);
            }
        }
        return '';
    };

    // Calculate total_rate from selected product's commission percentages
    const calculateTotalRate = () => {
        const selectedProduct = getSelectedProduct();
        if (selectedProduct) {
            return (selectedProduct.ops_commission_pct || 0) +
                   (selectedProduct.tech_commission_pct || 0) +
                   (selectedProduct.commission_agent_pct || 0);
        }
        return 0;
    };

    const validateForm = () => {
        if (!orderForm.shares || parseFloat(orderForm.shares) <= 0) {
            setEditOrderError(t('shares_required'));
            return false;
        }
        if (!orderForm.storage_cost || parseFloat(orderForm.storage_cost) < 0) {
            setEditOrderError(t('storage_cost_required'));
            return false;
        }
        if (!orderForm.total_rate || parseFloat(orderForm.total_rate) < 0) {
            setEditOrderError(t('total_rate_required'));
            return false;
        }
        return true;
    };

    const validateNewOrderForm = () => {
        if (!newOrderForm.product_id) {
            setAddOrderError(t('product_required'));
            return false;
        }
        if (!newOrderForm.customer_id) {
            setAddOrderError(t('customer_required'));
            return false;
        }
        if (!newOrderForm.shares || parseFloat(newOrderForm.shares) <= 0) {
            setAddOrderError(t('shares_required'));
            return false;
        }
        if (!newOrderForm.storage_cost || parseFloat(newOrderForm.storage_cost) < 0) {
            setAddOrderError(t('storage_cost_required'));
            return false;
        }

        // Check if product has enough available shares
        const selectedProduct = getSelectedProduct();
        if (selectedProduct) {
            const soldShares = selectedProduct.sold_shares || 0;
            const totalShares = selectedProduct.total_shares || 0;
            if (soldShares >= totalShares) {
                setAddOrderError(t('product_sold_out'));
                return false;
            }

            const requestedShares = parseFloat(newOrderForm.shares);
            const availableShares = totalShares - soldShares;
            if (requestedShares > availableShares) {
                setAddOrderError(t('insufficient_shares_available', { available: availableShares }));
                return false;
            }
        }

        return true;
    };

    // Add Order Functions
    const handleAddOrder = () => {
        setShowAddOrderModal(true);
        setNewOrderForm({
            product_id: '',
            customer_id: '',
            agent_id: '',
            shares: '',
            storage_cost: '',
            start_at: '',
            end_at: '',
            review_status: 'pending'
        });
        setAddOrderError('');
        setAddOrderSuccess('');
    };

    const handleConfirmAddOrder = async () => {
        if (!validateNewOrderForm()) {
            return;
        }

        setAddOrderLoading(true);
        setAddOrderError('');
        setAddOrderSuccess('');

        try {
            const supabase = getSupabase();
            if (!supabase) {
                throw new Error('Database connection failed');
            }

            // Get current user (maker)
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) {
                throw new Error('Maker not authenticated');
            }

            // Get selected product for commission percentages
            const selectedProduct = getSelectedProduct();
            if (!selectedProduct) {
                throw new Error('Selected product not found');
            }

            // Calculate total_rate from product commission percentages
            const totalRate = calculateTotalRate();

            // Prepare new order data
            const newOrderData = {
                product_id: newOrderForm.product_id,
                customer_id: newOrderForm.customer_id,
                agent_id: newOrderForm.agent_id || null,
                shares: parseFloat(newOrderForm.shares),
                storage_cost: parseFloat(newOrderForm.storage_cost),
                total_rate: totalRate,
                tech_fee_pct: selectedProduct.tech_commission_pct || 0,
                sales_fee_pct: selectedProduct.commission_agent_pct || 0,
                ops_fee_pct: selectedProduct.ops_commission_pct || 0,
                start_at: newOrderForm.start_at || null,
                end_at: newOrderForm.end_at || null,
                review_status: newOrderForm.review_status
            };

            // Create order
            const { data, error } = await supabase
                .from('orders')
                .insert(newOrderData)
                .select(`
                    id,
                    product_id,
                    shares,
                    storage_cost,
                    total_rate,
                    start_at,
                    end_at,
                    review_status,
                    created_at,
                    products ( name, maker_id ),
                    customer_profiles ( real_name ),
                    agent_profiles ( brand_name )
                `)
                .single();

            if (error) {
                throw error;
            }

            setAddOrderSuccess(t('order_created_successfully'));

            // Add new order to orders list
            setOrders(prev => [data, ...prev]);

            // Close modal after 2 seconds
            setTimeout(() => {
                setShowAddOrderModal(false);
            }, 2000);

        } catch (error) {
            console.error('Error creating order:', error);
            setAddOrderError(error.message || t('order_creation_error'));
        } finally {
            setAddOrderLoading(false);
        }
    };

    const closeAddOrderModal = () => {
        setShowAddOrderModal(false);
        setNewOrderForm({
            product_id: '',
            customer_id: '',
            agent_id: '',
            shares: '',
            storage_cost: '',
            start_at: '',
            end_at: '',
            review_status: 'pending'
        });
        setAddOrderError('');
        setAddOrderSuccess('');
    };

    // Edit Order Functions
    const handleEditOrder = (order) => {
        setEditingOrder(order);
        setOrderForm({
            shares: order.shares?.toString() || '',
            storage_cost: order.storage_cost?.toString() || '',
            total_rate: order.total_rate?.toString() || '',
            start_at: order.start_at || '',
            end_at: order.end_at || '',
            review_status: order.review_status || 'pending'
        });
        setEditOrderError('');
        setEditOrderSuccess('');
        setShowEditOrderModal(true);
    };

    const handleConfirmEditOrder = async () => {
        if (!validateForm()) {
            return;
        }

        setEditOrderLoading(true);
        setEditOrderError('');
        setEditOrderSuccess('');

        try {
            const supabase = getSupabase();
            if (!supabase) {
                throw new Error('Database connection failed');
            }

            // Get current user (maker)
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) {
                throw new Error('Maker not authenticated');
            }

            // Store old review status for audit log
            const oldReviewStatus = editingOrder.review_status;
            const newReviewStatus = orderForm.review_status;

            // Prepare updated order data
            const updatedOrderData = {
                shares: parseFloat(orderForm.shares),
                storage_cost: parseFloat(orderForm.storage_cost),
                total_rate: parseFloat(orderForm.total_rate),
                start_at: orderForm.start_at || null,
                end_at: orderForm.end_at || null,
                review_status: newReviewStatus,
                updated_at: new Date().toISOString()
            };

            // Update order
            const { data, error } = await supabase
                .from('orders')
                .update(updatedOrderData)
                .eq('id', editingOrder.id)
                .select(`
                    id,
                    product_id,
                    shares,
                    storage_cost,
                    total_rate,
                    start_at,
                    end_at,
                    review_status,
                    created_at,
                    updated_at,
                    products ( name, maker_id ),
                    customer_profiles ( 
                    real_name,
                    users (
                        email
                        ) 
                    ),
                    agent_profiles ( brand_name )
                `)
                .single();

            if (error) {
                throw error;
            }

            // Update product sold_shares based on order status changes
            if (oldReviewStatus !== newReviewStatus) {
                try {
                    // Get current product data
                    const { data: productData, error: productSelectError } = await supabase
                        .from('products')
                        .select('sold_shares')
                        .eq('id', editingOrder.product_id)
                        .single();

                    if (productSelectError) {
                        console.error('Error fetching product data:', productSelectError);
                    } else {
                        const oldSoldShares = productData.sold_shares || 0;
                        let newSoldShares = oldSoldShares;
                        let updateReason = '';

                        // If status changed from non-approved to approved, add shares
                        if (oldReviewStatus !== 'approved' && newReviewStatus === 'approved') {
                            newSoldShares = oldSoldShares + parseFloat(orderForm.shares);
                            updateReason = `Order ${editingOrder.id} approved with ${orderForm.shares} shares`;
                        }
                        // If status changed from approved to non-approved, subtract shares
                        else if (oldReviewStatus === 'approved' && newReviewStatus !== 'approved') {
                            newSoldShares = Math.max(0, oldSoldShares - parseFloat(editingOrder.shares));
                            updateReason = `Order ${editingOrder.id} status changed from approved to ${newReviewStatus}, removing ${editingOrder.shares} shares`;
                        }

                        // Only update if sold_shares actually changed
                        if (newSoldShares !== oldSoldShares) {
                            // Update product sold_shares
                            const { error: productUpdateError } = await supabase
                                .from('products')
                                .update({ sold_shares: newSoldShares })
                                .eq('id', editingOrder.product_id);

                            if (productUpdateError) {
                                console.error('Error updating product sold_shares:', productUpdateError);
                            } else {
                                // Insert audit log for product sold_shares update
                                const productAuditLogData = {
                                    user_id: user.id,
                                    action: 'sold_shares_updated',
                                    object_table: 'products',
                                    object_id: editingOrder.product_id,
                                    diff: {
                                        old: { sold_shares: oldSoldShares },
                                        new: { sold_shares: newSoldShares },
                                        reason: updateReason
                                    }
                                };

                                const { error: productAuditError } = await supabase
                                    .from('audit_logs')
                                    .insert(productAuditLogData);

                                if (productAuditError) {
                                    console.error('Error inserting product audit log:', productAuditError);
                                }
                            }
                        }
                    }
                } catch (productError) {
                    console.error('Error in product sold_shares update process:', productError);
                }
            }

            // Insert audit log if review status changed
            if (oldReviewStatus !== newReviewStatus) {
                const auditLogData = {
                    user_id: user.id,
                    action: newReviewStatus,
                    object_table: 'orders',
                    object_id: editingOrder.id,
                    diff: {
                        old: { review_status: oldReviewStatus },
                        new: { review_status: newReviewStatus }
                    }
                };

                const { error: auditError } = await supabase
                    .from('audit_logs')
                    .insert(auditLogData);

                if (auditError) {
                    console.error('Error inserting audit log:', auditError);
                }
            }

            setEditOrderSuccess(t('order_updated_successfully'));

            // Update orders list
            setOrders(prev => prev.map(order =>
                order.id === editingOrder.id ? data : order
            ));

            // Close modal after 2 seconds
            setTimeout(() => {
                setShowEditOrderModal(false);
            }, 2000);

        } catch (error) {
            console.error('Error updating order:', error);
            setEditOrderError(error.message || t('order_update_error'));
        } finally {
            setEditOrderLoading(false);
        }
    };

    const closeEditOrderModal = () => {
        setShowEditOrderModal(false);
        setEditingOrder(null);
        setOrderForm({
            shares: '',
            storage_cost: '',
            total_rate: '',
            start_at: '',
            end_at: '',
            review_status: 'pending'
        });
        setEditOrderError('');
        setEditOrderSuccess('');
    };

    // Delete Order Functions
    const handleDeleteOrder = (order) => {
        setDeletingOrder(order);
        setDeletePassword('');
        setDeleteOrderError('');
        setDeleteOrderSuccess('');
        setShowDeleteOrderModal(true);
    };

    const handleConfirmDeleteOrder = async () => {
        if (!deletePassword.trim()) {
            setDeleteOrderError(t('password_required'));
            return;
        }

        setDeleteOrderLoading(true);
        setDeleteOrderError('');
        setDeleteOrderSuccess('');

        try {
            const supabase = getSupabase();
            if (!supabase) {
                throw new Error('Database connection failed');
            }

            // Get current user (maker)
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) {
                throw new Error('Maker not authenticated');
            }

            // Verify password by attempting to sign in
            const { error: signInError } = await supabase.auth.signInWithPassword({
                email: user.email,
                password: deletePassword,
            });

            if (signInError) {
                throw new Error(t('password_incorrect'));
            }

            // Update order to set deleted_at = now (soft delete)
            const { error } = await supabase
                .from('orders')
                .update({
                    deleted_at: new Date().toISOString()
                })
                .eq('id', deletingOrder.id)
                .select('id')
                .single();

            if (error) {
                throw error;
            }

            setDeleteOrderSuccess(t('order_deleted_successfully'));

            // Remove order from list
            setOrders(prev => prev.filter(order => order.id !== deletingOrder.id));

            // Close modal after 2 seconds
            setTimeout(() => {
                setShowDeleteOrderModal(false);
            }, 2000);

        } catch (error) {
            console.error('Error deleting order:', error);
            setDeleteOrderError(error.message || t('order_delete_error'));
        } finally {
            setDeleteOrderLoading(false);
        }
    };

    const closeDeleteOrderModal = () => {
        setShowDeleteOrderModal(false);
        setDeletingOrder(null);
        setDeletePassword('');
        setDeleteOrderError('');
        setDeleteOrderSuccess('');
    };

    if (loading) {
        return <div>{t('loading_orders_text')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('all_orders')}</h2>

            {/* Top Operation Bar */}
            <Row className="mb-4">
                <Col>
                    <Card>
                        <Card.Body>
                            <Row className="align-items-end">
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('export_data')}</Form.Label>
                                        <div>
                                            <Button
                                                variant="success"
                                                onClick={exportToCSV}
                                                className="mb-2 me-2"
                                                disabled={filteredOrders.length === 0}
                                            >
                                                <FaDownload className="me-1" />
                                                {t('export_all')}
                                            </Button>
                                            <Button
                                                variant="primary"
                                                onClick={handleAddOrder}
                                                className="mb-2"
                                            >
                                                <FaPlus className="me-1" />
                                                {t('add_order')}
                                            </Button>
                                        </div>
                                    </Form.Group>
                                </Col>
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('search_customer')}</Form.Label>
                                        <InputGroup>
                                            <Form.Control
                                                type="text"
                                                placeholder={t('search_by_email_or_name')}
                                                value={searchTerm}
                                                onChange={(e) => setSearchTerm(e.target.value)}
                                            />
                                        </InputGroup>
                                    </Form.Group>
                                </Col>
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('review_status')}</Form.Label>
                                        <Form.Select
                                            value={reviewStatus}
                                            onChange={(e) => setReviewStatus(e.target.value)}
                                        >
                                            <option value="">{t('all_status')}</option>
                                            <option value="pending">{t('pending_review')}</option>
                                            <option value="approved">{t('approved')}</option>
                                            <option value="rejected">{t('rejected')}</option>
                                        </Form.Select>
                                    </Form.Group>
                                </Col>
                                <Col md={3}>
                                    <Form.Group>
                                        <Form.Label>{t('start_date')}</Form.Label>
                                        <Form.Control
                                            type="date"
                                            value={startDate}
                                            onChange={(e) => setStartDate(e.target.value)}
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md={3}>
                                    <Form.Group>
                                        <Form.Label>{t('end_date')}</Form.Label>
                                        <Form.Control
                                            type="date"
                                            value={endDate}
                                            onChange={(e) => setEndDate(e.target.value)}
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('order_id')}</th>
                                        <th>{t('product_name')}</th>
                                        <th>{t('customer')}</th>
                                        <th>{t('agent')}</th>
                                        <th>{t('shares')}</th>
                                        <th>{t('storage_cost')}</th>
                                        <th>{t('proof_image')}</th>
                                        <th>{t('status')}</th>
                                        <th>{t('created_at')}</th>
                                        <th>{t('actions')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {paginatedOrders.length === 0 ? (
                                        <tr>
                                            <td colSpan="11" className="text-center">{t('no_orders_available')}</td>
                                        </tr>
                                    ) : (
                                        paginatedOrders.map(order => (
                                            <tr key={order.id}>
                                                <td>{order.id.substring(0, 8)}...</td>
                                                <td>{order.products?.name || 'N/A'}</td>
                                                <td>{order.customer_profiles?.real_name || order.customer_profiles?.users?.email || 'N/A'}</td>
                                                <td>{order.agent_profiles?.brand_name || 'N/A'}</td>
                                                <td>{order.shares}</td>
                                                <td>{order.storage_cost}</td>
                                                <td>
                                                    {order.proof_image_url ? (
                                                        <Button
                                                            variant="outline-primary"
                                                            size="sm"
                                                            onClick={() => handleViewImage(order.proof_image_url)}
                                                            title={t('view_proof_image')}
                                                        >
                                                            <FaImage />
                                                        </Button>
                                                    ) : (
                                                        <span className="text-muted">-</span>
                                                    )}
                                                </td>
                                                <td><StatusBadge status={order.review_status} type="review" /></td>
                                                <td>{new Date(order.created_at).toLocaleString()}</td>
                                                <td>
                                                    <Button
                                                        variant="info"
                                                        size="sm"
                                                        className="me-2"
                                                        onClick={() => handleEditOrder(order)}
                                                    >
                                                        {t('edit')}
                                                    </Button>
                                                    <Button
                                                        variant="danger"
                                                        size="sm"
                                                        onClick={() => handleDeleteOrder(order)}
                                                    >
                                                        {t('delete')}
                                                    </Button>
                                                </td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>

                            {/* Pagination */}
                            {totalPages > 1 && (
                                <div className="d-flex justify-content-center">
                                    <Pagination>
                                        <Pagination.First
                                            onClick={() => handlePageChange(1)}
                                            disabled={currentPage === 1}
                                        />
                                        <Pagination.Prev
                                            onClick={() => handlePageChange(currentPage - 1)}
                                            disabled={currentPage === 1}
                                        />

                                        {/* Show page numbers */}
                                        {[...Array(totalPages)].map((_, index) => {
                                            const pageNumber = index + 1;
                                            // Show first page, last page, current page, and pages around current page
                                            if (
                                                pageNumber === 1 ||
                                                pageNumber === totalPages ||
                                                (pageNumber >= currentPage - 2 && pageNumber <= currentPage + 2)
                                            ) {
                                                return (
                                                    <Pagination.Item
                                                        key={pageNumber}
                                                        active={pageNumber === currentPage}
                                                        onClick={() => handlePageChange(pageNumber)}
                                                    >
                                                        {pageNumber}
                                                    </Pagination.Item>
                                                );
                                            } else if (
                                                pageNumber === currentPage - 3 ||
                                                pageNumber === currentPage + 3
                                            ) {
                                                return <Pagination.Ellipsis key={pageNumber} />;
                                            }
                                            return null;
                                        })}

                                        <Pagination.Next
                                            onClick={() => handlePageChange(currentPage + 1)}
                                            disabled={currentPage === totalPages}
                                        />
                                        <Pagination.Last
                                            onClick={() => handlePageChange(totalPages)}
                                            disabled={currentPage === totalPages}
                                        />
                                    </Pagination>
                                </div>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Add Order Modal */}
            <Modal show={showAddOrderModal} onHide={closeAddOrderModal} size="lg">
                <Modal.Header closeButton={false} className="custom-modal-header d-flex justify-content-between">
                    <Modal.Title>{t('add_order')}</Modal.Title>
                    <Button
                        variant="outline-light"
                        className="btn-close-custom"
                        onClick={closeAddOrderModal}
                        title="Close"
                    >
                        <FaTimes />
                    </Button>
                </Modal.Header>
                <Modal.Body>
                    {addOrderError && (
                        <Alert variant="danger" className="mb-3">
                            {addOrderError}
                        </Alert>
                    )}
                    {addOrderSuccess && (
                        <Alert variant="success" className="mb-3">
                            {addOrderSuccess}
                        </Alert>
                    )}

                    <Form>
                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('product')}</strong></Form.Label>
                                    <Form.Select
                                        value={newOrderForm.product_id}
                                        onChange={(e) => handleNewOrderFormChange('product_id', e.target.value)}
                                        disabled={addOrderLoading}
                                        required
                                    >
                                        <option value="">{t('select_product')}</option>
                                        {products.map(product => (
                                            <option key={product.id} value={product.id}>
                                                {product.name} ({product.category})
                                            </option>
                                        ))}
                                    </Form.Select>
                                    {(() => {
                                        const selectedProduct = getSelectedProduct();
                                        if (selectedProduct) {
                                            const soldShares = selectedProduct.sold_shares || 0;
                                            const totalShares = selectedProduct.total_shares || 0;
                                            const availableShares = totalShares - soldShares;
                                            const isSoldOut = soldShares >= totalShares;

                                            return (
                                                <div className="mt-2">
                                                    <small className="text-muted">
                                                        {t('price')}: {selectedProduct.price} | {' '}
                                                        {t('available_shares')}: {availableShares} / {totalShares}
                                                        {isSoldOut && (
                                                            <span className="text-danger ms-2">
                                                                <strong>({t('sold_out')})</strong>
                                                            </span>
                                                        )}
                                                    </small>
                                                </div>
                                            );
                                        }
                                        return null;
                                    })()}
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('customer')}</strong></Form.Label>
                                    <Form.Select
                                        value={newOrderForm.customer_id}
                                        onChange={(e) => handleNewOrderFormChange('customer_id', e.target.value)}
                                        disabled={addOrderLoading}
                                        required
                                    >
                                        <option value="">{t('select_customer')}</option>
                                        {customers.map(customer => (
                                            <option key={customer.user_id} value={customer.user_id}>
                                                {customer.real_name || customer.users?.email} ({customer.users?.email})
                                            </option>
                                        ))}
                                    </Form.Select>
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('agent')}</strong></Form.Label>
                                    <Form.Select
                                        value={newOrderForm.agent_id}
                                        onChange={(e) => handleNewOrderFormChange('agent_id', e.target.value)}
                                        disabled={addOrderLoading}
                                    >
                                        <option value="">{t('select_agent_optional')}</option>
                                        {agents.map(agent => (
                                            <option key={agent.user_id} value={agent.user_id}>
                                                {agent.brand_name} ({agent.users?.email})
                                            </option>
                                        ))}
                                    </Form.Select>
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('shares')}</strong></Form.Label>
                                    <Form.Control
                                        type="number"
                                        value={newOrderForm.shares}
                                        onChange={(e) => handleNewOrderFormChange('shares', e.target.value)}
                                        placeholder={t('enter_shares')}
                                        disabled={addOrderLoading}
                                        min="0"
                                        step="0.01"
                                        required
                                    />
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('storage_cost')}</strong></Form.Label>
                                    <Form.Control
                                        type="text"
                                        value={newOrderForm.storage_cost || ''}
                                        disabled={true}
                                        readOnly
                                        className="bg-light"
                                        placeholder={t('auto_calculated')}
                                    />
                                    <Form.Text className="text-muted">
                                        {t('auto_calculated_price_x_shares')}
                                    </Form.Text>
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('total_rate')}</strong></Form.Label>
                                    <Form.Control
                                        type="text"
                                        value={calculateTotalRate().toFixed(4)}
                                        disabled={true}
                                        readOnly
                                        className="bg-light"
                                    />
                                    <Form.Text className="text-muted">
                                        {t('auto_calculated_from_product')}
                                    </Form.Text>
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('review_status')}</strong></Form.Label>
                                    <Form.Select
                                        value={newOrderForm.review_status}
                                        onChange={(e) => handleNewOrderFormChange('review_status', e.target.value)}
                                        disabled={addOrderLoading}
                                    >
                                        <option value="pending">{t('pending_review')}</option>
                                        <option value="approved">{t('approved')}</option>
                                        <option value="rejected">{t('rejected')}</option>
                                    </Form.Select>
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('start_date')}</strong></Form.Label>
                                    <Form.Control
                                        type="date"
                                        value={newOrderForm.start_at}
                                        onChange={(e) => handleNewOrderFormChange('start_at', e.target.value)}
                                        disabled={addOrderLoading}
                                    />
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('end_date')}</strong></Form.Label>
                                    <Form.Control
                                        type="date"
                                        value={newOrderForm.end_at}
                                        onChange={(e) => handleNewOrderFormChange('end_at', e.target.value)}
                                        disabled={addOrderLoading}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={closeAddOrderModal} disabled={addOrderLoading}>
                        {t('cancel')}
                    </Button>
                    <Button
                        variant="primary"
                        onClick={handleConfirmAddOrder}
                        disabled={addOrderLoading || !newOrderForm.product_id || !newOrderForm.customer_id || !newOrderForm.shares || !newOrderForm.storage_cost}
                    >
                        {addOrderLoading ? (
                            <>
                                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                {t('creating')}
                            </>
                        ) : (
                            t('create_order')
                        )}
                    </Button>
                </Modal.Footer>
            </Modal>

            {/* Edit Order Modal */}
            <Modal show={showEditOrderModal} onHide={closeEditOrderModal} size="lg">
                <Modal.Header closeButton={false} className="custom-modal-header d-flex justify-content-between">
                    <Modal.Title>{t('edit_order')}</Modal.Title>
                    <Button
                        variant="outline-light"
                        className="btn-close-custom"
                        onClick={closeEditOrderModal}
                        title="Close"
                    >
                        <FaTimes />
                    </Button>
                </Modal.Header>
                <Modal.Body>
                    {editOrderError && (
                        <Alert variant="danger" className="mb-3">
                            {editOrderError}
                        </Alert>
                    )}
                    {editOrderSuccess && (
                        <Alert variant="success" className="mb-3">
                            {editOrderSuccess}
                        </Alert>
                    )}

                    <Form>
                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('shares')}</strong></Form.Label>
                                    <Form.Control
                                        type="number"
                                        value={orderForm.shares}
                                        onChange={(e) => handleFormChange('shares', e.target.value)}
                                        placeholder={t('enter_shares')}
                                        disabled={editOrderLoading}
                                        min="0"
                                        step="0.01"
                                        required
                                    />
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('storage_cost')}</strong></Form.Label>
                                    <Form.Control
                                        type="number"
                                        value={orderForm.storage_cost}
                                        onChange={(e) => handleFormChange('storage_cost', e.target.value)}
                                        placeholder={t('enter_storage_cost')}
                                        disabled={editOrderLoading}
                                        min="0"
                                        step="0.000001"
                                        required
                                    />
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('total_rate')}</strong></Form.Label>
                                    <Form.Control
                                        type="number"
                                        value={orderForm.total_rate}
                                        onChange={(e) => handleFormChange('total_rate', e.target.value)}
                                        placeholder={t('enter_total_rate')}
                                        disabled={editOrderLoading}
                                        min="0"
                                        step="0.0001"
                                        required
                                    />
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('start_date')}</strong></Form.Label>
                                    <Form.Control
                                        type="date"
                                        value={orderForm.start_at}
                                        onChange={(e) => handleFormChange('start_at', e.target.value)}
                                        disabled={editOrderLoading}
                                    />
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('end_date')}</strong></Form.Label>
                                    <Form.Control
                                        type="date"
                                        value={orderForm.end_at}
                                        onChange={(e) => handleFormChange('end_at', e.target.value)}
                                        disabled={editOrderLoading}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={12}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('review_status')}</strong></Form.Label>
                                    <Form.Select
                                        value={orderForm.review_status}
                                        onChange={(e) => handleFormChange('review_status', e.target.value)}
                                        disabled={editOrderLoading}
                                    >
                                        <option value="pending">{t('pending_review')}</option>
                                        <option value="approved">{t('approved')}</option>
                                        <option value="rejected">{t('rejected')}</option>
                                    </Form.Select>
                                </Form.Group>
                            </Col>
                        </Row>
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={closeEditOrderModal} disabled={editOrderLoading}>
                        {t('cancel')}
                    </Button>
                    <Button
                        variant="primary"
                        onClick={handleConfirmEditOrder}
                        disabled={editOrderLoading || !orderForm.shares || !orderForm.storage_cost || !orderForm.total_rate}
                    >
                        {editOrderLoading ? (
                            <>
                                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                {t('updating')}
                            </>
                        ) : (
                            t('update_order')
                        )}
                    </Button>
                </Modal.Footer>
            </Modal>

            {/* Delete Order Modal */}
            <Modal show={showDeleteOrderModal} onHide={closeDeleteOrderModal}>
                <Modal.Header closeButton={false} className="custom-modal-header d-flex justify-content-between">
                    <Modal.Title>{t('delete_order')}</Modal.Title>
                    <Button
                        variant="outline-light"
                        className="btn-close-custom"
                        onClick={closeDeleteOrderModal}
                        title="Close"
                    >
                        <FaTimes />
                    </Button>
                </Modal.Header>
                <Modal.Body>
                    {deleteOrderError && (
                        <Alert variant="danger" className="mb-3">
                            {deleteOrderError}
                        </Alert>
                    )}
                    {deleteOrderSuccess && (
                        <Alert variant="success" className="mb-3">
                            {deleteOrderSuccess}
                        </Alert>
                    )}

                    <p>{t('delete_order_confirmation')}</p>
                    {deletingOrder && (
                        <div className="mb-3">
                            <strong>{t('order_id')}: </strong>{deletingOrder.id.substring(0, 8)}...<br />
                            <strong>{t('product_name')}: </strong>{deletingOrder.products?.name || 'N/A'}<br />
                            <strong>{t('shares')}: </strong>{deletingOrder.shares}<br />
                            <strong>{t('customer')}: </strong>{deletingOrder.customer_profiles?.real_name || 'N/A'}
                        </div>
                    )}

                    <Form.Group className="mb-3">
                        <Form.Label><strong>{t('enter_password_to_confirm')}</strong></Form.Label>
                        <Form.Control
                            type="password"
                            value={deletePassword}
                            onChange={(e) => setDeletePassword(e.target.value)}
                            placeholder={t('password')}
                            disabled={deleteOrderLoading}
                            required
                        />
                    </Form.Group>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={closeDeleteOrderModal} disabled={deleteOrderLoading}>
                        {t('cancel')}
                    </Button>
                    <Button
                        variant="danger"
                        onClick={handleConfirmDeleteOrder}
                        disabled={deleteOrderLoading || !deletePassword.trim()}
                    >
                        {deleteOrderLoading ? (
                            <>
                                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                {t('deleting')}
                            </>
                        ) : (
                            t('delete_order')
                        )}
                    </Button>
                </Modal.Footer>
            </Modal>

            {/* Image View Modal */}
            <Modal show={showImageModal} onHide={() => setShowImageModal(false)} size="lg" centered>
                <Modal.Header closeButton>
                    <Modal.Title>{t('proof_image')}</Modal.Title>
                </Modal.Header>
                <Modal.Body className="text-center">
                    {selectedImage && (
                        <img
                            src={selectedImage}
                            alt={t('proof_image')}
                            style={{ maxWidth: '100%', maxHeight: '70vh', objectFit: 'contain' }}
                        />
                    )}
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={() => setShowImageModal(false)}>
                        {t('close')}
                    </Button>
                </Modal.Footer>
            </Modal>
        </Container>
    );
};

export default MakerOrderListPage;
