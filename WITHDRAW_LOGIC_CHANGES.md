# Withdraw Logic Changes

## 修改概述

修改了withdraw的逻辑，从原来的"审批后扣款"改为"提交后立即扣款"。

## 原有逻辑

1. **提交申请时**：只创建withdrawal记录，不扣款
2. **审批通过时**：从user_assets扣款，增加withdrawn_total
3. **审批拒绝时**：不做任何user_assets操作

## 新逻辑

1. **提交申请时**：立即从user_assets扣款（balance_available和balance_total），但不增加withdrawn_total
2. **审批通过时**：只增加withdrawn_total，不再扣款
3. **审批拒绝时**：将之前扣掉的钱加回user_assets（balance_available和balance_total）

## 修改的文件

### 1. 后端API：`../includes/api-routes.php`

在`process_withdrawal`方法中添加了立即扣款逻辑：

```php
// First, deduct the amount from user_assets immediately upon submission
$new_balance_available = $available_balance - $amount;
$new_balance_total = $assets_data[0]['balance_total'] - $amount;

// Update user_assets to deduct the withdrawal amount
$assets_update_response = wp_remote_request($supabase_url . '/rest/v1/user_assets?user_id=eq.' . $supabase_user_id . '&currency_code=eq.FIL', [
    'method' => 'PATCH',
    'headers' => [
        'apikey' => $supabase_service_key,
        'Authorization' => 'Bearer ' . $supabase_service_key,
        'Content-Type' => 'application/json',
        'Prefer' => 'return=representation'
    ],
    'body' => json_encode([
        'balance_available' => $new_balance_available,
        'balance_total' => $new_balance_total
    ])
]);
```

### 2. Maker审批页面：`src/pages/maker/WithdrawList.js`

修改了`handleWithdrawalDecision`方法：

- **审批通过时**：只更新withdrawn_total
- **审批拒绝时**：将金额加回balance_available和balance_total

### 3. Agent审批页面：`src/pages/agent/WithdrawList.js`

与Maker页面相同的修改逻辑。

### 4. 客户页面：`src/pages/customer/MyAccountPage.js`

在提交成功后添加了资产数据刷新，让用户能立即看到余额变化。

## 优势

1. **用户体验更好**：用户提交申请后立即看到余额减少，避免混淆
2. **资金管理更严格**：防止用户在审批期间重复提交或使用已申请的资金
3. **逻辑更清晰**：提交即扣款，审批只决定是否退还

## 测试场景

### 场景1：正常提交和审批通过
1. 用户余额：100 FIL
2. 提交10 FIL withdraw申请
3. 提交后：balance_available = 90, balance_total = 90, withdrawn_total = 0
4. 审批通过后：balance_available = 90, balance_total = 90, withdrawn_total = 9.99

### 场景2：提交后审批拒绝
1. 用户余额：100 FIL
2. 提交10 FIL withdraw申请
3. 提交后：balance_available = 90, balance_total = 90, withdrawn_total = 0
4. 审批拒绝后：balance_available = 100, balance_total = 100, withdrawn_total = 0

## 注意事项

1. 确保在提交申请时有足够的余额验证
2. 审批拒绝时要使用request_amount而不是final_amount来退还资金
3. 审批通过时要使用final_amount来更新withdrawn_total
4. 添加了回滚机制：如果创建withdrawal记录失败，会自动回滚user_assets的扣款

## 错误处理和回滚机制

在后端API中添加了回滚逻辑：
- 如果在扣款成功后，创建withdrawal记录失败
- 系统会自动将扣掉的金额加回user_assets
- 防止出现"钱被扣了但没有withdrawal记录"的情况

## 修改完成

所有必要的文件都已修改完成：
1. ✅ 后端API立即扣款逻辑
2. ✅ Maker审批页面逻辑修改
3. ✅ Agent审批页面逻辑修改
4. ✅ 客户页面资产刷新
5. ✅ 错误回滚机制

新的withdraw逻辑已经实现，用户提交申请后会立即看到余额减少，审批通过后增加withdrawn_total，审批拒绝后退还资金。
