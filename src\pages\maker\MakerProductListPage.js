
import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Button, Badge, Modal, Form, Alert, InputGroup, Pagination } from 'react-bootstrap';
import { getSupabase, getCurrentMakerId } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';
import { FaTimes, FaPlus, FaDownload } from "react-icons/fa";
import StatusBadge from '../../components/StatusBadge';

const MakerProductListPage = () => {
    const { t } = useTranslation();
    const [products, setProducts] = useState([]);
    const [loading, setLoading] = useState(true);
    const [miners, setMiners] = useState([]);

    // Filter states
    const [statusFilter, setStatusFilter] = useState('');
    const [startDate, setStartDate] = useState('');
    const [endDate, setEndDate] = useState('');
    const [filteredProducts, setFilteredProducts] = useState([]);

    // Pagination states
    const [currentPage, setCurrentPage] = useState(1);
    const [productsPerPage] = useState(10);
    const [paginatedProducts, setPaginatedProducts] = useState([]);

    // Add Product Modal states
    const [showAddProductModal, setShowAddProductModal] = useState(false);
    const [addProductLoading, setAddProductLoading] = useState(false);
    const [addProductError, setAddProductError] = useState('');
    const [addProductSuccess, setAddProductSuccess] = useState('');

    // Edit Product Modal states
    const [showEditProductModal, setShowEditProductModal] = useState(false);
    const [editProductLoading, setEditProductLoading] = useState(false);
    const [editProductError, setEditProductError] = useState('');
    const [editProductSuccess, setEditProductSuccess] = useState('');
    const [editingProduct, setEditingProduct] = useState(null);

    // Delete Product Modal states
    const [showDeleteProductModal, setShowDeleteProductModal] = useState(false);
    const [deleteProductLoading, setDeleteProductLoading] = useState(false);
    const [deleteProductError, setDeleteProductError] = useState('');
    const [deleteProductSuccess, setDeleteProductSuccess] = useState('');
    const [deletingProduct, setDeletingProduct] = useState(null);
    const [deletePassword, setDeletePassword] = useState('');

    // Form data
    const [productForm, setProductForm] = useState({
        name: '',
        total_shares: '',
        miner_id: '',
        price: '',
        effective_delay_days: '0',
        min_purchase: '',
        partner_reward_pct: '0',
        ops_commission_pct: '0.25',
        tech_commission_pct: '0.05',
        commission_agent_pct: '0',
        review_status: 'pending'
    });

    useEffect(() => {
        const fetchData = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);

            // Get maker ID (works for both maker and technician roles)
            const makerId = await getCurrentMakerId();
            if (!makerId) {
                setLoading(false);
                return; // User not authenticated or not authorized
            }

            // Fetch products
            const { data: productsData, error: productsError } = await supabase
                .from('products')
                .select(`
                    id,
                    name,
                    category,
                    price,
                    total_shares,
                    sold_shares,
                    miner_id,
                    effective_delay_days,
                    min_purchase,
                    partner_reward_pct,
                    ops_commission_pct,
                    tech_commission_pct,
                    commission_agent_pct,
                    is_disabled,
                    duration_days,
                    review_status,
                    created_at
                `)
                .eq('maker_id', makerId)
                .eq('is_disabled', false)
                .order('created_at', { ascending: false });

            if (productsError) {
                console.error('Error fetching products:', productsError);
            } else {
                setProducts(productsData);
            }

            // Fetch miners
            const { data: minersData, error: minersError } = await supabase
                .from('miners')
                .select(`
                    id,
                    category,
                    filecoin_miner_id,
                    facilities (
                        name
                    )
                `)
                .order('created_at', { ascending: false });

            if (minersError) {
                console.error('Error fetching miners:', minersError);
            } else {
                setMiners(minersData);
            }

            setLoading(false);
        };

        fetchData();
    }, []);

    // Filter products based on search criteria
    useEffect(() => {
        let filtered = products;

        // Filter by review status
        if (statusFilter) {
            filtered = filtered.filter(product => product.review_status === statusFilter);
        }

        // Filter by date range
        if (startDate) {
            filtered = filtered.filter(product =>
                new Date(product.created_at) >= new Date(startDate)
            );
        }
        if (endDate) {
            filtered = filtered.filter(product =>
                new Date(product.created_at) <= new Date(endDate)
            );
        }

        setFilteredProducts(filtered);
        setCurrentPage(1); // Reset to first page when filters change
    }, [products, statusFilter, startDate, endDate]);

    // Paginate filtered products
    useEffect(() => {
        const indexOfLastProduct = currentPage * productsPerPage;
        const indexOfFirstProduct = indexOfLastProduct - productsPerPage;
        const currentProducts = filteredProducts.slice(indexOfFirstProduct, indexOfLastProduct);
        setPaginatedProducts(currentProducts);
    }, [filteredProducts, currentPage, productsPerPage]);

    // Pagination handlers
    const handlePageChange = (pageNumber) => {
        setCurrentPage(pageNumber);
    };

    const totalPages = Math.ceil(filteredProducts.length / productsPerPage);

    // Export products to CSV
    const exportToCSV = () => {
        if (filteredProducts.length === 0) {
            alert(t('no_data_to_export'));
            return;
        }

        // Define CSV headers
        const headers = [
            t('product_id'),
            t('name'),
            t('category'),
            t('price_per_share'),
            t('total_shares'),
            t('sold_shares'),
            t('remaining_shares'),
            t('miner_id'),
            t('effective_delay_days'),
            t('min_purchase'),
            t('partner_reward_pct'),
            t('ops_commission_pct'),
            t('tech_commission_pct'),
            t('commission_agent_pct'),
            t('duration_days'),
            t('status'),
            t('review_status'),
            t('created_at')
        ];

        // Convert data to CSV format
        const csvData = filteredProducts.map(product => [
            product.id,
            product.name || '-',
            product.category || '-',
            product.price?.toFixed(2) || '0.00',
            product.total_shares || '0',
            product.sold_shares || '0',
            (product.total_shares - product.sold_shares) || '0',
            product.miner_id || '-',
            product.effective_delay_days || '0',
            product.min_purchase || '0',
            (product.partner_reward_pct * 100)?.toFixed(2) + '%' || '0.00%',
            (product.ops_commission_pct * 100)?.toFixed(2) + '%' || '25.00%',
            (product.tech_commission_pct * 100)?.toFixed(2) + '%' || '5.00%',
            (product.commission_agent_pct * 100)?.toFixed(2) + '%' || '0.00%',
            product.duration_days || '1',
            product.is_disabled ? t('disabled') : t('enabled'),
            t(product.review_status) || 'pending',
            new Date(product.created_at).toLocaleString()
        ]);

        // Combine headers and data
        const csvContent = [headers, ...csvData]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');

        // Add UTF-8 BOM to ensure proper encoding for Japanese characters
        const BOM = '\uFEFF';
        const csvWithBOM = BOM + csvContent;

        // Create and download CSV file with proper UTF-8 encoding
        const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `maker_products_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const handleAddProduct = () => {
        setShowAddProductModal(true);
        setProductForm({
            name: '',
            total_shares: '',
            miner_id: '',
            price: '',
            effective_delay_days: '0',
            min_purchase: '',
            partner_reward_pct: '0',
            ops_commission_pct: '0.25',
            tech_commission_pct: '0.05',
            commission_agent_pct: '0',
            review_status: 'pending'
        });
        setAddProductError('');
        setAddProductSuccess('');
    };

    const handleFormChange = (field, value) => {
        setProductForm(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const validateForm = () => {
        if (!productForm.name.trim()) {
            setAddProductError(t('product_name_required'));
            return false;
        }
        if (!productForm.total_shares || parseFloat(productForm.total_shares) <= 0) {
            setAddProductError(t('total_shares_required'));
            return false;
        }
        if (!productForm.miner_id) {
            setAddProductError(t('miner_selection_required'));
            return false;
        }
        if (!productForm.price || parseFloat(productForm.price) <= 0) {
            setAddProductError(t('price_required'));
            return false;
        }
        if (!productForm.min_purchase || parseFloat(productForm.min_purchase) <= 0) {
            setAddProductError(t('min_purchase_required'));
            return false;
        }
        if (parseFloat(productForm.min_purchase) > parseFloat(productForm.total_shares)) {
            setAddProductError(t('min_purchase_cannot_exceed_total_shares'));
            return false;
        }
        return true;
    };

    const handleConfirmAddProduct = async () => {
        if (!validateForm()) {
            return;
        }

        setAddProductLoading(true);
        setAddProductError('');
        setAddProductSuccess('');

        try {
            const supabase = getSupabase();
            if (!supabase) {
                throw new Error('Database connection failed');
            }

            // Get maker ID (works for both maker and technician roles)
            const makerId = await getCurrentMakerId();
            if (!makerId) {
                throw new Error('User not authenticated or not authorized');
            }

            // Get default technician for this maker
            const { data: technicianProfile, error: techError } = await supabase
                .from('technician_profiles')
                .select('user_id')
                .eq('maker_id', makerId)
                .limit(1)
                .single();

            if (techError) {
                console.error('Error fetching technician profile:', techError);
                throw new Error('No technician found for this maker');
            }

            // Prepare product data
            const productData = {
                maker_id: makerId,
                technician_id: technicianProfile.user_id,
                category: productForm.category || 'FIL',
                name: productForm.name.trim(),
                total_shares: parseFloat(productForm.total_shares),
                miner_id: productForm.miner_id,
                price: parseFloat(productForm.price),
                effective_delay_days: parseInt(productForm.effective_delay_days) || 0,
                min_purchase: parseFloat(productForm.min_purchase),
                sold_shares: 0,
                partner_reward_pct: parseFloat(productForm.partner_reward_pct) || 0,
                ops_commission_pct: parseFloat(productForm.ops_commission_pct) || 0.25,
                tech_commission_pct: parseFloat(productForm.tech_commission_pct) || 0.05,
                commission_agent_pct: parseFloat(productForm.commission_agent_pct) || 0,
                duration_days: parseInt(productForm.duration_days) || 1,
                is_disabled: false,
                self_distribution: true,
                auto_distribution: true,
                review_status: 'pending'
            };

            // Insert product
            const { data, error } = await supabase
                .from('products')
                .insert(productData)
                .select(`
                        id,
                        name,
                        category,
                        price,
                        total_shares,
                        sold_shares,
                        is_disabled,
                        review_status,
                        created_at,
                        duration_days,
                        maker_profiles ( domain )
                    `)
                .single();

            if (error) {
                throw error;
            }

            setAddProductSuccess(t('product_created_successfully'));

            // Update products list
            setProducts(prev => [data, ...prev]);

            // Close modal after 2 seconds
            setTimeout(() => {
                setShowAddProductModal(false);
            }, 2000);

        } catch (error) {
            console.error('Error creating product:', error);
            setAddProductError(error.message || t('product_creation_error'));
        } finally {
            setAddProductLoading(false);
        }
    };

    const closeAddProductModal = () => {
        setShowAddProductModal(false);
        setProductForm({
            name: '',
            total_shares: '',
            miner_id: '',
            price: '',
            effective_delay_days: '0',
            min_purchase: '',
            partner_reward_pct: '0',
            ops_commission_pct: '0.25',
            tech_commission_pct: '0.05',
            commission_agent_pct: '0',
            duration_days: '1',
            category: 'FIL'
        });
        setAddProductError('');
        setAddProductSuccess('');
    };

    // Edit Product Functions
    const handleEditProduct = (product) => {
        setEditingProduct(product);
        setProductForm({
            name: product.name || '',
            total_shares: product.total_shares?.toString() || '',
            miner_id: product.miner_id || '',
            price: product.price?.toString() || '',
            effective_delay_days: product.effective_delay_days?.toString() || '0',
            min_purchase: product.min_purchase?.toString() || '',
            partner_reward_pct: product.partner_reward_pct?.toString() || '0',
            ops_commission_pct: product.ops_commission_pct?.toString() || '0.25',
            tech_commission_pct: product.tech_commission_pct?.toString() || '0.05',
            commission_agent_pct: product.commission_agent_pct?.toString() || '0',
            review_status: product.review_status || 'pending',
            duration_days: product.duration_days?.toString() || '1',
            category: product.category || 'FIL'
        });
        setEditProductError('');
        setEditProductSuccess('');
        setShowEditProductModal(true);
    };

    const handleConfirmEditProduct = async () => {
        if (!validateForm()) {
            return;
        }

        setEditProductLoading(true);
        setEditProductError('');
        setEditProductSuccess('');

        try {
            const supabase = getSupabase();
            if (!supabase) {
                throw new Error('Database connection failed');
            }

            // Get current user (maker)
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) {
                throw new Error('Maker not authenticated');
            }

            // Store old review status for audit log
            const oldReviewStatus = editingProduct.review_status;
            const newReviewStatus = productForm.review_status;

            // Prepare updated product data (excluding id and maker_id)
            const updatedProductData = {
                name: productForm.name.trim(),
                total_shares: parseFloat(productForm.total_shares),
                miner_id: productForm.miner_id,
                price: parseFloat(productForm.price),
                effective_delay_days: parseInt(productForm.effective_delay_days) || 0,
                min_purchase: parseFloat(productForm.min_purchase),
                partner_reward_pct: parseFloat(productForm.partner_reward_pct) || 0,
                ops_commission_pct: parseFloat(productForm.ops_commission_pct) || 0.25,
                tech_commission_pct: parseFloat(productForm.tech_commission_pct) || 0.05,
                commission_agent_pct: parseFloat(productForm.commission_agent_pct) || 0,
                duration_days: parseInt(productForm.duration_days) || 1,
                category: productForm.category || 'FIL',
                review_status: newReviewStatus
            };

            // Update product
            const { data, error } = await supabase
                .from('products')
                .update(updatedProductData)
                .eq('id', editingProduct.id)
                .eq('maker_id', user.id) // Ensure only maker can edit their own products
                .select(`
                    id,
                    name,
                    category,
                    price,
                    total_shares,
                    sold_shares,
                    miner_id,
                    effective_delay_days,
                    min_purchase,
                    partner_reward_pct,
                    ops_commission_pct,
                    tech_commission_pct,
                    commission_agent_pct,
                    is_disabled,
                    duration_days,
                    review_status,
                    created_at
                `)
                .single();

            if (error) {
                throw error;
            }

            // Insert audit log if review status changed
            if (oldReviewStatus !== newReviewStatus) {
                const auditLogData = {
                    user_id: user.id, // Current user (maker) who made the change
                    action: newReviewStatus, // The new review status (approved, rejected, pending, etc.)
                    object_table: 'products',
                    object_id: editingProduct.id, // The product that was updated
                    diff: {
                        old: { review_status: oldReviewStatus },
                        new: { review_status: newReviewStatus }
                    }
                };

                const { error: auditError } = await supabase
                    .from('audit_logs')
                    .insert(auditLogData);

                if (auditError) {
                    console.error('Error inserting audit log:', auditError);
                    // Don't throw error here as the main operation succeeded
                    // Just log the error for debugging
                }
            }

            setEditProductSuccess(t('product_updated_successfully'));

            // Update products list
            setProducts(prev => prev.map(product =>
                product.id === editingProduct.id ? data : product
            ));

            // Close modal after 2 seconds
            setTimeout(() => {
                setShowEditProductModal(false);
            }, 2000);

        } catch (error) {
            console.error('Error updating product:', error);
            setEditProductError(error.message || t('product_update_error'));
        } finally {
            setEditProductLoading(false);
        }
    };

    const closeEditProductModal = () => {
        setShowEditProductModal(false);
        setEditingProduct(null);
        setProductForm({
            name: '',
            total_shares: '',
            miner_id: '',
            price: '',
            effective_delay_days: '0',
            min_purchase: '',
            partner_reward_pct: '0',
            ops_commission_pct: '0.25',
            tech_commission_pct: '0.05',
            commission_agent_pct: '0',
            review_status: 'pending',
            duration_days: '1',
            category: 'FIL'
        });
        setEditProductError('');
        setEditProductSuccess('');
    };

    // Delete Product Functions
    const handleDeleteProduct = (product) => {
        setDeletingProduct(product);
        setDeletePassword('');
        setDeleteProductError('');
        setDeleteProductSuccess('');
        setShowDeleteProductModal(true);
    };

    const handleConfirmDeleteProduct = async () => {
        if (!deletePassword.trim()) {
            setDeleteProductError(t('password_required'));
            return;
        }

        setDeleteProductLoading(true);
        setDeleteProductError('');
        setDeleteProductSuccess('');

        try {
            const supabase = getSupabase();
            if (!supabase) {
                throw new Error('Database connection failed');
            }

            // Get current user (maker)
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) {
                throw new Error('Maker not authenticated');
            }

            // Verify password by attempting to sign in
            const { error: signInError } = await supabase.auth.signInWithPassword({
                email: user.email,
                password: deletePassword,
            });

            if (signInError) {
                throw new Error(t('password_incorrect'));
            }

            // Update product to set is_disabled = true and delisted_at = now
            const { error } = await supabase
                .from('products')
                .update({
                    is_disabled: true,
                    delisted_at: new Date().toISOString()
                })
                .eq('id', deletingProduct.id)
                .eq('maker_id', user.id) // Ensure only maker can delete their own products
                .select('id')
                .single();

            if (error) {
                throw error;
            }

            setDeleteProductSuccess(t('product_delisted_successfully'));

            // Remove product from list (since we only show non-disabled products)
            setProducts(prev => prev.filter(product => product.id !== deletingProduct.id));

            // Close modal after 2 seconds
            setTimeout(() => {
                setShowDeleteProductModal(false);
            }, 2000);

        } catch (error) {
            console.error('Error delisting product:', error);
            setDeleteProductError(error.message || t('product_delete_error'));
        } finally {
            setDeleteProductLoading(false);
        }
    };

    const closeDeleteProductModal = () => {
        setShowDeleteProductModal(false);
        setDeletingProduct(null);
        setDeletePassword('');
        setDeleteProductError('');
        setDeleteProductSuccess('');
    };

    if (loading) {
        return <div>{t('loading_products')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('my_products')}</h2>

            {/* Filter Bar */}
            <Row className="mb-4">
                <Col>
                    <Card>
                        <Card.Body>
                            <Row className="align-items-end">
                                <Col md={3}>
                                    <div className="d-flex gap-2 mb-2">
                                        <Button
                                            variant="success"
                                            onClick={exportToCSV}
                                            disabled={filteredProducts.length === 0}
                                        >
                                            <FaDownload className="me-1" />
                                            {t('export_all')}
                                        </Button>
                                        <Button
                                            variant="primary"
                                            onClick={handleAddProduct}
                                        >
                                            <FaPlus className="me-1" />
                                            {t('add_new_product')}
                                        </Button>
                                    </div>
                                </Col>
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('review_status')}</Form.Label>
                                        <Form.Select
                                            value={statusFilter}
                                            onChange={(e) => setStatusFilter(e.target.value)}
                                        >
                                            <option value="">{t('all_status')}</option>
                                            <option value="pending">{t('pending_review')}</option>
                                            <option value="approved">{t('approved')}</option>
                                            <option value="rejected">{t('rejected')}</option>
                                        </Form.Select>
                                    </Form.Group>
                                </Col>
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('start_date')}</Form.Label>
                                        <Form.Control
                                            type="date"
                                            value={startDate}
                                            onChange={(e) => setStartDate(e.target.value)}
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('end_date')}</Form.Label>
                                        <Form.Control
                                            type="date"
                                            value={endDate}
                                            onChange={(e) => setEndDate(e.target.value)}
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('product_id')}</th>
                                        <th>{t('name')}</th>
                                        <th>{t('category')}</th>
                                        <th>{t('price_per_share')}</th>
                                        <th>{t('total_shares')}</th>
                                        <th>{t('sold_shares')}</th>
                                        <th>{t('status')}</th>
                                        <th>{t('review_status')}</th>
                                        <th>{t('created_at')}</th>
                                        <th>{t('actions')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {paginatedProducts.length === 0 ? (
                                        <tr>
                                            <td colSpan="10" className="text-center">{t('no_products_available')}</td>
                                        </tr>
                                    ) : (
                                        paginatedProducts.map(product => (
                                            <tr key={product.id}>
                                                <td>{product.id.substring(0, 8)}...</td>
                                                <td>{product.name}</td>
                                                <td><Badge>{product.category}</Badge></td>
                                                <td>{product.price}</td>
                                                <td>{product.total_shares}</td>
                                                <td>{product.sold_shares}</td>
                                                <td><Badge bg={product.is_disabled ? 'danger' : 'success'}>{product.is_disabled ? t('disabled') : t('enabled')}</Badge></td>
                                                <td><StatusBadge status={product.review_status} type="review" />
                                                </td>
                                                <td>{new Date(product.created_at).toLocaleString()}</td>
                                                <td>
                                                    <Button
                                                        variant="info"
                                                        size="sm"
                                                        className="me-2"
                                                        onClick={() => handleEditProduct(product)}
                                                    >
                                                        {t('edit')}
                                                    </Button>
                                                    <Button
                                                        variant="danger"
                                                        size="sm"
                                                        onClick={() => handleDeleteProduct(product)}
                                                    >
                                                        {t('delete')}
                                                    </Button>
                                                </td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>

                            {/* Pagination */}
                            {totalPages > 1 && (
                                <div className="d-flex justify-content-center">
                                    <Pagination>
                                        <Pagination.First
                                            onClick={() => handlePageChange(1)}
                                            disabled={currentPage === 1}
                                        />
                                        <Pagination.Prev
                                            onClick={() => handlePageChange(currentPage - 1)}
                                            disabled={currentPage === 1}
                                        />

                                        {/* Show page numbers */}
                                        {[...Array(totalPages)].map((_, index) => {
                                            const pageNumber = index + 1;
                                            // Show first page, last page, current page, and pages around current page
                                            if (
                                                pageNumber === 1 ||
                                                pageNumber === totalPages ||
                                                (pageNumber >= currentPage - 2 && pageNumber <= currentPage + 2)
                                            ) {
                                                return (
                                                    <Pagination.Item
                                                        key={pageNumber}
                                                        active={pageNumber === currentPage}
                                                        onClick={() => handlePageChange(pageNumber)}
                                                    >
                                                        {pageNumber}
                                                    </Pagination.Item>
                                                );
                                            } else if (
                                                pageNumber === currentPage - 3 ||
                                                pageNumber === currentPage + 3
                                            ) {
                                                return <Pagination.Ellipsis key={pageNumber} />;
                                            }
                                            return null;
                                        })}

                                        <Pagination.Next
                                            onClick={() => handlePageChange(currentPage + 1)}
                                            disabled={currentPage === totalPages}
                                        />
                                        <Pagination.Last
                                            onClick={() => handlePageChange(totalPages)}
                                            disabled={currentPage === totalPages}
                                        />
                                    </Pagination>
                                </div>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Add Product Modal */}
            <Modal show={showAddProductModal} onHide={closeAddProductModal} size="lg">
                <Modal.Header closeButton={false} className="custom-modal-header d-flex justify-content-between">
                    <Modal.Title>{t('add_new_product')}</Modal.Title>
                    <Button
                        variant="outline-light"
                        className="btn-close-custom"
                        onClick={closeAddProductModal}
                        title="Close"
                    >
                        <FaTimes />
                    </Button>
                </Modal.Header>
                <Modal.Body>
                    {addProductError && (
                        <Alert variant="danger" className="mb-3">
                            {addProductError}
                        </Alert>
                    )}
                    {addProductSuccess && (
                        <Alert variant="success" className="mb-3">
                            {addProductSuccess}
                        </Alert>
                    )}

                    <Form>
                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('product_name')}</strong></Form.Label>
                                    <Form.Control
                                        type="text"
                                        value={productForm.name}
                                        onChange={(e) => handleFormChange('name', e.target.value)}
                                        placeholder={t('enter_product_name')}
                                        disabled={addProductLoading}
                                        required
                                    />
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('total_shares')}</strong></Form.Label>
                                    <Form.Control
                                        type="number"
                                        value={productForm.total_shares}
                                        onChange={(e) => handleFormChange('total_shares', e.target.value)}
                                        placeholder={t('enter_total_shares')}
                                        disabled={addProductLoading}
                                        min="1"
                                        step="1"
                                        required
                                    />
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('miner_selection')}</strong></Form.Label>
                                    <Form.Select
                                        value={productForm.miner_id}
                                        onChange={(e) => handleFormChange('miner_id', e.target.value)}
                                        disabled={addProductLoading}
                                        required
                                    >
                                        <option value="">{t('select_miner')}</option>
                                        {miners.map(miner => (
                                            <option key={miner.id} value={miner.id}>
                                                {miner.filecoin_miner_id} ({miner.category})
                                            </option>
                                        ))}
                                    </Form.Select>
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('price')}</strong></Form.Label>
                                    <Form.Control
                                        type="number"
                                        value={productForm.price}
                                        onChange={(e) => handleFormChange('price', e.target.value)}
                                        placeholder={t('enter_price')}
                                        disabled={addProductLoading}
                                        min="0"
                                        step="0.01"
                                        required
                                    />
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('effective_delay_days')}</strong></Form.Label>
                                    <Form.Control
                                        type="number"
                                        value={productForm.effective_delay_days}
                                        onChange={(e) => handleFormChange('effective_delay_days', e.target.value)}
                                        placeholder="0"
                                        disabled={addProductLoading}
                                        min="0"
                                        step="1"
                                    />
                                    <Form.Text className="text-muted">
                                        {t('default_0_days')}
                                    </Form.Text>
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('min_purchase')}</strong></Form.Label>
                                    <Form.Control
                                        type="number"
                                        value={productForm.min_purchase}
                                        onChange={(e) => handleFormChange('min_purchase', e.target.value)}
                                        placeholder={t('enter_min_purchase')}
                                        disabled={addProductLoading}
                                        min="1"
                                        step="1"
                                        required
                                    />
                                    <Form.Text className="text-muted">
                                        {t('cannot_exceed_total_shares')}
                                    </Form.Text>
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('product_category')}</strong></Form.Label>
                                    <Form.Control
                                        type="text"
                                        value={productForm.category}
                                        onChange={(e) => handleFormChange('category', e.target.value)}
                                        placeholder="FIL"
                                        disabled={addProductLoading}
                                        required
                                    />
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('duration_days')}</strong></Form.Label>
                                    <Form.Control
                                        type="number"
                                        value={productForm.duration_days}
                                        onChange={(e) => handleFormChange('duration_days', e.target.value)}
                                        placeholder={t('enter_duration_days')}
                                        disabled={addProductLoading}
                                        min="1"
                                        step="1"
                                        required
                                    />
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('partner_reward_pct')}</strong></Form.Label>
                                    <Form.Control
                                        type="number"
                                        value={productForm.partner_reward_pct}
                                        onChange={(e) => handleFormChange('partner_reward_pct', e.target.value)}
                                        placeholder="0"
                                        disabled={addProductLoading}
                                        min="0"
                                        max="1"
                                        step="0.01"
                                    />
                                    <Form.Text className="text-muted">
                                        {t('default_0_percent')}
                                    </Form.Text>
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('ops_commission_pct')}</strong></Form.Label>
                                    <Form.Control
                                        type="number"
                                        value={productForm.ops_commission_pct}
                                        onChange={(e) => handleFormChange('ops_commission_pct', e.target.value)}
                                        placeholder="0.25"
                                        disabled={addProductLoading}
                                        min="0"
                                        max="1"
                                        step="0.01"
                                    />
                                    <Form.Text className="text-muted">
                                        {t('default_25_percent')}
                                    </Form.Text>
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('tech_commission_pct')}</strong></Form.Label>
                                    <Form.Control
                                        type="number"
                                        value={productForm.tech_commission_pct}
                                        onChange={(e) => handleFormChange('tech_commission_pct', e.target.value)}
                                        placeholder="0.05"
                                        disabled={addProductLoading}
                                        min="0"
                                        max="1"
                                        step="0.01"
                                    />
                                    <Form.Text className="text-muted">
                                        {t('default_5_percent')}
                                    </Form.Text>
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('commission_agent_pct')}</strong></Form.Label>
                                    <Form.Control
                                        type="number"
                                        value={productForm.commission_agent_pct}
                                        onChange={(e) => handleFormChange('commission_agent_pct', e.target.value)}
                                        placeholder="0"
                                        disabled={addProductLoading}
                                        min="0"
                                        max="1"
                                        step="0.01"
                                    />
                                    <Form.Text className="text-muted">
                                        {t('default_1_percent')}
                                    </Form.Text>
                                </Form.Group>
                            </Col>
                        </Row>
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={closeAddProductModal} disabled={addProductLoading}>
                        {t('cancel')}
                    </Button>
                    <Button
                        variant="primary"
                        onClick={handleConfirmAddProduct}
                        disabled={addProductLoading || !productForm.name || !productForm.total_shares || !productForm.miner_id || !productForm.price || !productForm.min_purchase}
                    >
                        {addProductLoading ? (
                            <>
                                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                {t('creating')}
                            </>
                        ) : (
                            t('create_product')
                        )}
                    </Button>
                </Modal.Footer>
            </Modal>

            {/* Edit Product Modal */}
            <Modal show={showEditProductModal} onHide={closeEditProductModal} size="lg">
                <Modal.Header closeButton={false} className="custom-modal-header d-flex justify-content-between">
                    <Modal.Title>{t('edit_product')}</Modal.Title>
                    <Button
                        variant="outline-light"
                        className="btn-close-custom"
                        onClick={closeEditProductModal}
                        title="Close"
                    >
                        <FaTimes />
                    </Button>
                </Modal.Header>
                <Modal.Body>
                    {editProductError && (
                        <Alert variant="danger" className="mb-3">
                            {editProductError}
                        </Alert>
                    )}
                    {editProductSuccess && (
                        <Alert variant="success" className="mb-3">
                            {editProductSuccess}
                        </Alert>
                    )}

                    <Form>
                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('product_name')}</strong></Form.Label>
                                    <Form.Control
                                        type="text"
                                        value={productForm.name}
                                        onChange={(e) => handleFormChange('name', e.target.value)}
                                        placeholder={t('enter_product_name')}
                                        disabled={editProductLoading}
                                        required
                                    />
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('total_shares')}</strong></Form.Label>
                                    <Form.Control
                                        type="number"
                                        value={productForm.total_shares}
                                        onChange={(e) => handleFormChange('total_shares', e.target.value)}
                                        placeholder={t('enter_total_shares')}
                                        disabled={editProductLoading}
                                        min="1"
                                        step="1"
                                        required
                                    />
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('miner_selection')}</strong></Form.Label>
                                    <Form.Select
                                        value={productForm.miner_id}
                                        onChange={(e) => handleFormChange('miner_id', e.target.value)}
                                        disabled={editProductLoading}
                                        required
                                    >
                                        <option value="">{t('select_miner')}</option>
                                        {miners.map(miner => (
                                            <option key={miner.id} value={miner.id}>
                                                {miner.filecoin_miner_id} ({miner.category})
                                            </option>
                                        ))}
                                    </Form.Select>
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('price')}</strong></Form.Label>
                                    <Form.Control
                                        type="number"
                                        value={productForm.price}
                                        onChange={(e) => handleFormChange('price', e.target.value)}
                                        placeholder={t('enter_price')}
                                        disabled={editProductLoading}
                                        min="0"
                                        step="0.01"
                                        required
                                    />
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('effective_delay_days')}</strong></Form.Label>
                                    <Form.Control
                                        type="number"
                                        value={productForm.effective_delay_days}
                                        onChange={(e) => handleFormChange('effective_delay_days', e.target.value)}
                                        placeholder="0"
                                        disabled={editProductLoading}
                                        min="0"
                                        step="1"
                                    />
                                    <Form.Text className="text-muted">
                                        {t('default_0_days')}
                                    </Form.Text>
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('min_purchase')}</strong></Form.Label>
                                    <Form.Control
                                        type="number"
                                        value={productForm.min_purchase}
                                        onChange={(e) => handleFormChange('min_purchase', e.target.value)}
                                        placeholder={t('enter_min_purchase')}
                                        disabled={editProductLoading}
                                        min="1"
                                        step="1"
                                        required
                                    />
                                    <Form.Text className="text-muted">
                                        {t('cannot_exceed_total_shares')}
                                    </Form.Text>
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('product_category')}</strong></Form.Label>
                                    <Form.Control
                                        type="text"
                                        value={productForm.category}
                                        onChange={(e) => handleFormChange('category', e.target.value)}
                                        placeholder="FIL"
                                        disabled={editProductLoading}
                                        required
                                    />
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('duration_days')}</strong></Form.Label>
                                    <Form.Control
                                        type="number"
                                        value={productForm.duration_days}
                                        onChange={(e) => handleFormChange('duration_days', e.target.value)}
                                        placeholder={t('enter_duration_days')}
                                        disabled={editProductLoading}
                                        min="1"
                                        step="1"
                                        required
                                    />
                                    <Form.Text className="text-muted">
                                        {t('cannot_exceed_total_shares')}
                                    </Form.Text>
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('partner_reward_pct')}</strong></Form.Label>
                                    <Form.Control
                                        type="number"
                                        value={productForm.partner_reward_pct}
                                        onChange={(e) => handleFormChange('partner_reward_pct', e.target.value)}
                                        placeholder="0"
                                        disabled={editProductLoading}
                                        min="0"
                                        max="1"
                                        step="0.01"
                                    />
                                    <Form.Text className="text-muted">
                                        {t('default_0_percent')}
                                    </Form.Text>
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('ops_commission_pct')}</strong></Form.Label>
                                    <Form.Control
                                        type="number"
                                        value={productForm.ops_commission_pct}
                                        onChange={(e) => handleFormChange('ops_commission_pct', e.target.value)}
                                        placeholder="0.25"
                                        disabled={editProductLoading}
                                        min="0"
                                        max="1"
                                        step="0.01"
                                    />
                                    <Form.Text className="text-muted">
                                        {t('default_25_percent')}
                                    </Form.Text>
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('tech_commission_pct')}</strong></Form.Label>
                                    <Form.Control
                                        type="number"
                                        value={productForm.tech_commission_pct}
                                        onChange={(e) => handleFormChange('tech_commission_pct', e.target.value)}
                                        placeholder="0.05"
                                        disabled={editProductLoading}
                                        min="0"
                                        max="1"
                                        step="0.01"
                                    />
                                    <Form.Text className="text-muted">
                                        {t('default_5_percent')}
                                    </Form.Text>
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('commission_agent_pct')}</strong></Form.Label>
                                    <Form.Control
                                        type="number"
                                        value={productForm.commission_agent_pct}
                                        onChange={(e) => handleFormChange('commission_agent_pct', e.target.value)}
                                        placeholder="0"
                                        disabled={editProductLoading}
                                        min="0"
                                        max="1"
                                        step="0.01"
                                    />
                                    <Form.Text className="text-muted">
                                        {t('default_1_percent')}
                                    </Form.Text>
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={12}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('review_status')}</strong></Form.Label>
                                    <Form.Select
                                        value={productForm.review_status}
                                        onChange={(e) => handleFormChange('review_status', e.target.value)}
                                        disabled={editProductLoading}
                                    >
                                        <option value="pending">{t('pending_review')}</option>
                                        <option value="approved">{t('approved')}</option>
                                        <option value="rejected">{t('rejected')}</option>
                                    </Form.Select>
                                    <Form.Text className="text-muted">
                                        {t('review_status_help')}
                                    </Form.Text>
                                </Form.Group>
                            </Col>
                        </Row>
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={closeEditProductModal} disabled={editProductLoading}>
                        {t('cancel')}
                    </Button>
                    <Button
                        variant="primary"
                        onClick={handleConfirmEditProduct}
                        disabled={editProductLoading || !productForm.name || !productForm.total_shares || !productForm.miner_id || !productForm.price || !productForm.min_purchase}
                    >
                        {editProductLoading ? (
                            <>
                                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                {t('updating')}
                            </>
                        ) : (
                            t('update_product')
                        )}
                    </Button>
                </Modal.Footer>
            </Modal>

            {/* Delete Product Modal */}
            <Modal show={showDeleteProductModal} onHide={closeDeleteProductModal}>
                <Modal.Header closeButton={false} className="custom-modal-header d-flex justify-content-between">
                    <Modal.Title>{t('delete_product')}</Modal.Title>
                    <Button
                        variant="outline-light"
                        className="btn-close-custom"
                        onClick={closeDeleteProductModal}
                        title="Close"
                    >
                        <FaTimes />
                    </Button>
                </Modal.Header>
                <Modal.Body>
                    {deleteProductError && (
                        <Alert variant="danger" className="mb-3">
                            {deleteProductError}
                        </Alert>
                    )}
                    {deleteProductSuccess && (
                        <Alert variant="success" className="mb-3">
                            {deleteProductSuccess}
                        </Alert>
                    )}

                    <p>{t('delete_product_confirmation')}</p>
                    {deletingProduct && (
                        <div className="mb-3">
                            <strong>{t('product_name')}: </strong>{deletingProduct.name}
                        </div>
                    )}

                    <Form.Group className="mb-3">
                        <Form.Label><strong>{t('enter_login_password')}</strong></Form.Label>
                        <Form.Control
                            type="password"
                            value={deletePassword}
                            onChange={(e) => setDeletePassword(e.target.value)}
                            placeholder={t('enter_password')}
                            disabled={deleteProductLoading}
                            required
                        />
                        <Form.Text className="text-muted">
                            {t('password_confirmation_required')}
                        </Form.Text>
                    </Form.Group>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={closeDeleteProductModal} disabled={deleteProductLoading}>
                        {t('cancel')}
                    </Button>
                    <Button
                        variant="danger"
                        onClick={handleConfirmDeleteProduct}
                        disabled={deleteProductLoading || !deletePassword.trim()}
                    >
                        {deleteProductLoading ? (
                            <>
                                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                {t('deleting')}
                            </>
                        ) : (
                            t('confirm_delete')
                        )}
                    </Button>
                </Modal.Footer>
            </Modal>
        </Container>
    );
};

export default MakerProductListPage;
