import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Button, Form, Pagination } from 'react-bootstrap';
import { FaDownload } from 'react-icons/fa';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';

const NetworkStats = () => {
    const { t } = useTranslation();
    const [stats, setStats] = useState([]);
    const [loading, setLoading] = useState(true);
    const [startDate, setStartDate] = useState('');
    const [endDate, setEndDate] = useState('');

    // Pagination states
    const [currentPage, setCurrentPage] = useState(1);
    const [statsPerPage] = useState(10);
    const [paginatedStats, setPaginatedStats] = useState([]);

    const fetchStats = async (start = null, end = null) => {
        const supabase = getSupabase();
        if (!supabase) return;

        setLoading(true);
        const { data: { user } } = await supabase.auth.getUser();

        if (!user) {
            setLoading(false);
            return; // User not logged in
        }

        // Build query with optional date filters
        let query = supabase
            .from('network_stats')
            .select(`
                stat_date,
                fil_per_tib
            `);

        // Add date filters if provided
        if (start) {
            query = query.gte('stat_date', start);
        }
        if (end) {
            query = query.lte('stat_date', end);
        }

        const { data, error } = await query.order('stat_date', { ascending: false });

        if (error) {
            console.error('Error fetching network stats:', error);
        } else {
            setStats(data);
        }
        setLoading(false);
    };

    useEffect(() => {
        fetchStats();
    }, []);

    // Paginate stats
    useEffect(() => {
        const indexOfLastStat = currentPage * statsPerPage;
        const indexOfFirstStat = indexOfLastStat - statsPerPage;
        const currentStats = stats.slice(indexOfFirstStat, indexOfLastStat);
        setPaginatedStats(currentStats);
    }, [stats, currentPage, statsPerPage]);

    const handleSearch = () => {
        fetchStats(startDate, endDate);
        setCurrentPage(1); // Reset to first page when searching
    };

    const handleReset = () => {
        setStartDate('');
        setEndDate('');
        setCurrentPage(1); // Reset to first page
        fetchStats();
    };

    // Pagination handlers
    const handlePageChange = (pageNumber) => {
        setCurrentPage(pageNumber);
    };

    const totalPages = Math.ceil(stats.length / statsPerPage);

    // Export stats to CSV
    const exportToCSV = () => {
        if (stats.length === 0) {
            alert(t('no_data_to_export'));
            return;
        }

        // Define CSV headers
        const headers = [
            t('stat_date'),
            t('fil_per_tib')
        ];

        // Convert data to CSV format
        const csvData = stats.map(stat => [
            new Date(stat.stat_date).toLocaleDateString(),
            stat.fil_per_tib ? Number(stat.fil_per_tib).toFixed(4) : '0'
        ]);

        // Combine headers and data
        const csvContent = [headers, ...csvData]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');

        // Add UTF-8 BOM to ensure proper encoding for Japanese characters
        const BOM = '\uFEFF';
        const csvWithBOM = BOM + csvContent;

        // Create and download CSV file with proper UTF-8 encoding
        const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `agent_network_stats_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    if (loading) {
        return <div>{t('loading_network_stats')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('network_stats')}</h2>

            {/* Date Range Query Form */}
            <Card className="mb-4">
                <Card.Body>
                    <Row className="align-items-end">
                        <Col md={2}>
                            <Form.Group>
                                <Form.Label>{t('export_data')}</Form.Label>
                                <div>
                                    <Button
                                        variant="success"
                                        onClick={exportToCSV}
                                        className="mb-2"
                                        disabled={stats.length === 0}
                                    >
                                        <FaDownload className="me-1" />
                                        {t('export_all')}
                                    </Button>
                                </div>
                            </Form.Group>
                        </Col>
                        <Col md={3}>
                            <Form.Group>
                                <Form.Label>{t('start_date')}</Form.Label>
                                <Form.Control
                                    type="date"
                                    value={startDate}
                                    onChange={(e) => setStartDate(e.target.value)}
                                />
                            </Form.Group>
                        </Col>
                        <Col md={3}>
                            <Form.Group>
                                <Form.Label>{t('end_date')}</Form.Label>
                                <Form.Control
                                    type="date"
                                    value={endDate}
                                    onChange={(e) => setEndDate(e.target.value)}
                                />
                            </Form.Group>
                        </Col>
                        <Col md={4}>
                            <div className="d-flex gap-2">
                                <Button variant="primary" onClick={handleSearch}>
                                    {t('search')}
                                </Button>
                                <Button variant="secondary" onClick={handleReset}>
                                    {t('reset')}
                                </Button>
                            </div>
                        </Col>
                    </Row>
                </Card.Body>
            </Card>

            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('stat_date')}</th>
                                        <th>{t('fil_per_tib')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {paginatedStats.length === 0 ? (
                                        <tr>
                                            <td colSpan="2" className="text-center">{t('no_network_stats_available')}</td>
                                        </tr>
                                    ) : (
                                        paginatedStats.map((stat) => (
                                            <tr key={`${stat.stat_date}`}>
                                                <td>{new Date(stat.stat_date).toLocaleDateString()}</td>
                                                <td>{stat.fil_per_tib ? Number(stat.fil_per_tib).toFixed(4) : '0'} FIL/TiB</td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>

                            {/* Pagination */}
                            {totalPages > 1 && (
                                <div className="d-flex justify-content-center">
                                    <Pagination>
                                        <Pagination.First
                                            onClick={() => handlePageChange(1)}
                                            disabled={currentPage === 1}
                                        />
                                        <Pagination.Prev
                                            onClick={() => handlePageChange(currentPage - 1)}
                                            disabled={currentPage === 1}
                                        />

                                        {/* Show page numbers */}
                                        {[...Array(totalPages)].map((_, index) => {
                                            const pageNumber = index + 1;
                                            // Show first page, last page, current page, and pages around current page
                                            if (
                                                pageNumber === 1 ||
                                                pageNumber === totalPages ||
                                                (pageNumber >= currentPage - 2 && pageNumber <= currentPage + 2)
                                            ) {
                                                return (
                                                    <Pagination.Item
                                                        key={pageNumber}
                                                        active={pageNumber === currentPage}
                                                        onClick={() => handlePageChange(pageNumber)}
                                                    >
                                                        {pageNumber}
                                                    </Pagination.Item>
                                                );
                                            } else if (
                                                pageNumber === currentPage - 3 ||
                                                pageNumber === currentPage + 3
                                            ) {
                                                return <Pagination.Ellipsis key={pageNumber} />;
                                            }
                                            return null;
                                        })}

                                        <Pagination.Next
                                            onClick={() => handlePageChange(currentPage + 1)}
                                            disabled={currentPage === totalPages}
                                        />
                                        <Pagination.Last
                                            onClick={() => handlePageChange(totalPages)}
                                            disabled={currentPage === totalPages}
                                        />
                                    </Pagination>
                                </div>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default NetworkStats;